"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowEngine = void 0;
const path_1 = __importDefault(require("path"));
const fs_extra_1 = __importDefault(require("fs-extra"));
const uuid_1 = require("uuid");
class WorkflowEngine {
    constructor(language = 'zh-CN', store) {
        this.createdDirectories = new Set();
        this.processedDirectories = new Set(); // 跟踪处理过程中遇到的所有目录
        this.cleanedEmptyDirectories = new Set(); // 跟踪被清理的空文件夹
        this.counterMap = new Map(); // 用于跟踪每个动作的counter值
        this.currentLanguage = 'zh-CN'; // 当前语言设置
        this.dirScanCache = new Map(); // 目录扫描缓存
        this.CACHE_EXPIRY_TIME = 5 * 60 * 1000; // 5分钟
        this.MAX_CACHE_SIZE = 100; // 最大缓存条目数
        this.isInterrupted = false; // 中断标志
        this.currentExecution = null; // 当前执行状态
        this.currentLanguage = language;
        this.store = store;
    }
    /**
     * 设置当前语言
     */
    setLanguage(language) {
        this.currentLanguage = language;
    }
    /**
     * 翻译文本
     */
    t(key, params) {
        const translations = {
            'zh-CN': {
                'workflow.configError': '工作流配置错误',
                'workflow.stepNoMatches': '步骤"{stepName}"没有找到匹配的{targetType}',
                'workflow.cannotProcessFiles': '工作流无法处理当前输入的文件',
                'workflow.checkStepConfig': '请检查工作流步骤配置',
                'workflow.onlyFiles': '输入了 {fileCount} 个文件，但所有工作流步骤都配置为处理文件夹',
                'workflow.onlyFolders': '输入了 {folderCount} 个文件夹，但所有工作流步骤都配置为处理文件',
                'workflow.addFoldersOrAdjust': '请添加文件夹或将步骤的处理目标调整为"文件"',
                'workflow.addFilesOrAdjust': '请添加文件或将步骤的处理目标调整为"文件夹"',
                'targetType.files': '文件',
                'targetType.folders': '文件夹',
                'error.permissionDenied': '权限不足，无法{operation}: {path}',
                'error.pathNotFound': '路径不存在: {path}',
                'error.directoryNotEmpty': '目录不为空，无法删除: {path}',
                'error.targetExists': '目标已存在: {path}',
                'error.crossDevice': '跨设备操作失败，请使用复制后删除: {path}',
                'error.pathTooLong': '路径名过长: {path}',
                'error.diskFull': '磁盘空间不足: {path}',
                'error.tooManyFiles': '打开的文件过多: {path}',
                'error.resourceBusy': '资源忙碌或被锁定: {path}',
                'error.generic': '操作失败: {path} - {error}',
                'workflow.interrupted': '工作流在步骤"{stepName}"前被中断'
            },
            'en-US': {
                'workflow.configError': 'Workflow configuration error',
                'workflow.stepNoMatches': 'Step "{stepName}" found no matching {targetType}',
                'workflow.cannotProcessFiles': 'Workflow cannot process current input files',
                'workflow.checkStepConfig': 'Please check workflow step configuration',
                'workflow.onlyFiles': 'Input {fileCount} files, but all workflow steps are configured to process folders',
                'workflow.onlyFolders': 'Input {folderCount} folders, but all workflow steps are configured to process files',
                'workflow.addFoldersOrAdjust': 'Please add folders or adjust step processing target to "files"',
                'workflow.addFilesOrAdjust': 'Please add files or adjust step processing target to "folders"',
                'targetType.files': 'files',
                'targetType.folders': 'folders',
                'error.permissionDenied': 'Permission denied, cannot {operation}: {path}',
                'error.pathNotFound': 'Path not found: {path}',
                'error.directoryNotEmpty': 'Directory not empty, cannot delete: {path}',
                'error.targetExists': 'Target already exists: {path}',
                'error.crossDevice': 'Cross-device operation failed, please use copy then delete: {path}',
                'error.pathTooLong': 'Path name too long: {path}',
                'error.diskFull': 'Disk space insufficient: {path}',
                'error.tooManyFiles': 'Too many open files: {path}',
                'error.resourceBusy': 'Resource busy or locked: {path}',
                'error.generic': 'Operation failed: {path} - {error}',
                'workflow.interrupted': 'Workflow interrupted before step "{stepName}"'
            }
        };
        let text = translations[this.currentLanguage][key] || key;
        // 如果有参数，进行替换
        if (params) {
            Object.keys(params).forEach(param => {
                text = text.replace(new RegExp(`\\{${param}\\}`, 'g'), params[param]);
            });
        }
        return text;
    }
    /**
     * 中断当前执行
     */
    interrupt() {
        this.isInterrupted = true;
        console.warn('[工作流引擎] 收到中断信号，将在当前步骤完成后停止');
    }
    /**
     * 重置中断状态
     */
    resetInterrupt() {
        this.isInterrupted = false;
    }
    /**
     * 获取当前执行状态（用于异常恢复）
     */
    getCurrentExecutionState() {
        return this.currentExecution;
    }
    /**
     * 保存部分执行结果（用于异常中断时的记录）
     */
    async savePartialResult() {
        if (!this.currentExecution) {
            return null;
        }
        const endTime = new Date().toISOString();
        const duration = new Date(endTime).getTime() - new Date(this.currentExecution.startTime).getTime();
        const partialResult = {
            workflowId: this.currentExecution.workflowId,
            startTime: this.currentExecution.startTime,
            endTime,
            duration,
            totalFiles: this.currentExecution.totalFiles,
            processedFiles: this.currentExecution.processedFiles,
            stepResults: this.currentExecution.stepResults,
            errors: [
                ...this.currentExecution.errors,
                {
                    file: '',
                    error: '工作流执行被中断',
                    step: 'system'
                }
            ]
        };
        console.warn('[工作流引擎] 保存部分执行结果:', {
            workflowId: partialResult.workflowId,
            processedFiles: partialResult.processedFiles,
            totalFiles: partialResult.totalFiles,
            stepCount: partialResult.stepResults.length
        });
        return partialResult;
    }
    /**
     * 清理过期的缓存条目
     */
    cleanupCache() {
        const now = Date.now();
        // 清理过期条目
        for (const [key, value] of this.dirScanCache.entries()) {
            if (now - value.timestamp > this.CACHE_EXPIRY_TIME) {
                this.dirScanCache.delete(key);
            }
        }
        // 如果缓存仍然太大，删除最旧的条目
        if (this.dirScanCache.size > this.MAX_CACHE_SIZE) {
            const entries = Array.from(this.dirScanCache.entries());
            entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
            const toDelete = entries.slice(0, this.dirScanCache.size - this.MAX_CACHE_SIZE);
            toDelete.forEach(([key]) => this.dirScanCache.delete(key));
        }
    }
    /**
     * 清理所有缓存
     */
    clearCache() {
        this.dirScanCache.clear();
        this.processedDirectories.clear();
        this.createdDirectories.clear();
        this.cleanedEmptyDirectories.clear();
        this.counterMap.clear();
    }
    /**
     * 翻译错误信息
     */
    translateError(errorMessage) {
        // 如果是英文模式，直接返回原始错误信息
        if (this.currentLanguage === 'en-US') {
            return errorMessage;
        }
        // 中文翻译映射
        const errorTranslations = {
            'Source and destination must not be the same.': '源路径和目标路径不能相同',
            'Cannot overwrite non-directory': '无法用目录覆盖非目录文件',
            'Cannot overwrite directory': '无法用非目录文件覆盖目录',
            'ENOENT: no such file or directory': '文件或目录不存在',
            'EACCES: permission denied': '权限被拒绝',
            'ENOSPC: no space left on device': '磁盘空间不足',
            'EMFILE: too many open files': '打开的文件过多',
            'EBUSY: resource busy or locked': '资源忙碌或被锁定',
            'EEXIST: file already exists': '文件已存在',
            'EISDIR: illegal operation on a directory': '对目录的非法操作',
            'ENOTDIR: not a directory': '不是一个目录',
            'EPERM: operation not permitted': '操作不被允许'
        };
        // 查找完全匹配的翻译
        if (errorTranslations[errorMessage]) {
            return errorTranslations[errorMessage];
        }
        // 查找部分匹配的翻译
        for (const [englishError, chineseError] of Object.entries(errorTranslations)) {
            if (errorMessage.includes(englishError)) {
                return errorMessage.replace(englishError, chineseError);
            }
        }
        // 如果没有找到翻译，返回原始错误信息
        return errorMessage;
    }
    /**
     * 验证路径安全性，防止路径遍历攻击
     */
    validatePath(inputPath) {
        try {
            // 规范化路径
            const normalizedPath = path_1.default.normalize(inputPath);
            // 检查是否包含路径遍历字符
            if (normalizedPath.includes('..') || normalizedPath.includes('~')) {
                console.warn(`Potentially unsafe path detected: ${inputPath}`);
                return false;
            }
            // 检查是否为绝对路径（在 Windows 和 Unix 系统上）
            if (!path_1.default.isAbsolute(normalizedPath)) {
                console.warn(`Relative path not allowed: ${inputPath}`);
                return false;
            }
            return true;
        }
        catch (error) {
            console.error(`Path validation error: ${error}`);
            return false;
        }
    }
    /**
     * 验证工作流配置
     */
    validateWorkflowConfiguration(workflow) {
        const errors = [];
        // 检查是否有启用的步骤
        const enabledSteps = workflow.steps.filter(s => s.enabled);
        if (enabledSteps.length === 0) {
            errors.push('工作流没有启用的步骤');
        }
        // 检查步骤配置
        for (const step of enabledSteps) {
            // 检查输入源配置
            if (step.inputSource.type === 'specific_path' && !step.inputSource.path) {
                errors.push(`步骤"${step.name}"配置了特定路径输入源但未指定路径`);
            }
            if (step.inputSource.type === 'previous_step' && step.inputSource.stepId) {
                const referencedStep = workflow.steps.find(s => s.id === step.inputSource.stepId);
                if (!referencedStep) {
                    errors.push(`步骤"${step.name}"引用了不存在的步骤`);
                }
                // 移除对未启用步骤的检查，因为这是合理的使用场景
            }
            // 检查动作配置
            const enabledActions = step.actions.filter(a => a.enabled);
            if (enabledActions.length === 0) {
                errors.push(`步骤"${step.name}"没有启用的动作`);
            }
            for (const action of enabledActions) {
                if ((action.type === 'move' || action.type === 'copy') && !action.config.targetPath) {
                    errors.push(`步骤"${step.name}"的${action.type === 'move' ? '移动' : '复制'}动作未配置目标路径`);
                }
            }
        }
        return { isValid: errors.length === 0, errors };
    }
    /**
     * 验证输入文件与工作流步骤的匹配性
     * 改进逻辑：只有当工作流完全无法处理任何输入文件时才报告严重问题
     */
    validateWorkflowInputs(files, workflow) {
        const issues = [];
        const enabledSteps = workflow.steps.filter(s => s.enabled).sort((a, b) => a.order - b.order);
        if (enabledSteps.length === 0) {
            return { isValid: true, issues: [] };
        }
        // 检查是否至少有一个步骤可以处理输入文件
        let hasAnyMatchingStep = false;
        const stepMatchInfo = [];
        for (const step of enabledSteps) {
            // 根据processTarget过滤文件
            const matchingFiles = this.filterFilesByProcessTarget(files, step.processTarget || 'files');
            const hasMatches = matchingFiles.length > 0;
            const targetType = this.t(step.processTarget === 'folders' ? 'targetType.folders' : 'targetType.files');
            stepMatchInfo.push({ step, hasMatches, targetType });
            if (hasMatches) {
                hasAnyMatchingStep = true;
            }
        }
        // 只有当所有步骤都没有匹配文件时，才报告为严重问题
        if (!hasAnyMatchingStep && files.length > 0) {
            // 分析输入文件类型 - 修复：使用正确的属性判断文件类型
            const fileCount = files.filter(f => !f.isDirectory).length;
            const folderCount = files.filter(f => f.isDirectory).length;
            let message = this.t('workflow.cannotProcessFiles');
            let suggestion = this.t('workflow.checkStepConfig');
            // 分析步骤需求
            const fileSteps = stepMatchInfo.filter(info => info.targetType === this.t('targetType.files'));
            const folderSteps = stepMatchInfo.filter(info => info.targetType === this.t('targetType.folders'));
            if (fileCount > 0 && folderCount === 0) {
                // 只有文件，但所有步骤都要求文件夹
                if (folderSteps.length === enabledSteps.length) {
                    message = this.t('workflow.onlyFiles', { fileCount });
                    suggestion = this.t('workflow.addFoldersOrAdjust');
                }
            }
            else if (folderCount > 0 && fileCount === 0) {
                // 只有文件夹，但所有步骤都要求文件
                if (fileSteps.length === enabledSteps.length) {
                    message = this.t('workflow.onlyFolders', { folderCount });
                    suggestion = this.t('workflow.addFilesOrAdjust');
                }
            }
            else if (fileCount > 0 && folderCount > 0) {
                // 有文件也有文件夹，但没有步骤能处理它们
                // 这种情况下给出更详细的分析
                const fileStepNames = fileSteps.map(info => info.step.name).join(', ');
                const folderStepNames = folderSteps.map(info => info.step.name).join(', ');
                message = this.t('workflow.mixedInputNoMatch', {
                    fileCount,
                    folderCount,
                    fileSteps: fileStepNames,
                    folderSteps: folderStepNames
                });
                suggestion = this.t('workflow.checkMixedStepConfig');
            }
            issues.push({
                type: 'no_matching_files',
                stepId: 'workflow',
                stepName: '整个工作流',
                processTarget: 'mixed',
                message,
                suggestion
            });
        }
        return { isValid: issues.length === 0, issues };
    }
    /**
     * 预览工作流执行结果
     */
    async preview(files, workflow) {
        // 验证工作流配置
        const validation = this.validateWorkflowConfiguration(workflow);
        if (!validation.isValid) {
            throw new Error(`${this.t('workflow.configError')}:\n${validation.errors.join('\n')}`);
        }
        // 验证输入文件与步骤的匹配性
        const inputValidation = this.validateWorkflowInputs(files, workflow);
        const startTime = new Date().toISOString();
        const stepResults = [];
        let currentFiles = [...files];
        const errors = [];
        // 如果有匹配性问题，添加到错误列表但继续执行（用于显示详细信息）
        if (!inputValidation.isValid) {
            for (const issue of inputValidation.issues) {
                errors.push({
                    file: '',
                    error: issue.message,
                    step: issue.stepId,
                    suggestion: issue.suggestion
                });
            }
        }
        for (const step of workflow.steps.filter(s => s.enabled).sort((a, b) => a.order - b.order)) {
            const stepStart = Date.now();
            // 获取步骤的输入文件
            const inputFiles = await this.getStepInputFiles(currentFiles, step, stepResults);
            // 处理步骤
            const { outputFiles, stepErrors, hasMatches } = await this.processStepPreview(inputFiles, step);
            // 处理步骤没有匹配文件的情况
            if (!hasMatches) {
                const targetType = this.t(step.processTarget === 'folders' ? 'targetType.folders' : 'targetType.files');
                if (inputFiles.length === 0) {
                    // 步骤没有输入文件（可能是前面的步骤过滤掉了所有文件）
                    stepErrors.push({
                        file: '',
                        error: this.t('workflow.stepNoInput', { stepName: step.name }) + ' - ' + this.t('workflow.checkPreviousSteps'),
                        step: step.id
                    });
                }
                else {
                    // 步骤有输入文件但没有匹配的文件
                    stepErrors.push({
                        file: '',
                        error: this.t('workflow.stepNoMatches', { stepName: step.name, targetType }) + ' - ' + this.t('workflow.adjustStepTarget'),
                        step: step.id
                    });
                }
            }
            const stepResult = {
                stepId: step.id,
                stepName: step.name,
                inputFiles,
                outputFiles,
                processedCount: outputFiles.filter(f => f.newPath && f.newPath !== f.path).length,
                errors: stepErrors,
                duration: Date.now() - stepStart
            };
            stepResults.push(stepResult);
            errors.push(...stepErrors);
            // 更新当前文件列表为步骤输出
            currentFiles = outputFiles;
        }
        const endTime = new Date().toISOString();
        // 计算实际处理的文件数量（所有步骤中实际被处理的文件总数）
        const totalProcessedFiles = stepResults.reduce((total, stepResult) => {
            // 计算该步骤中实际被处理的文件数量
            // 只要文件状态为success且有newPath字段，就算作已处理
            const processedInStep = stepResult.outputFiles.filter(file => file.status === 'success' && file.newPath).length;
            return total + processedInStep;
        }, 0);
        return {
            workflowId: workflow.id,
            stepResults,
            totalFiles: files.length,
            processedFiles: totalProcessedFiles,
            errors,
            startTime,
            endTime,
            duration: Date.parse(endTime) - Date.parse(startTime)
        };
    }
    /**
     * 批处理执行工作流
     */
    async executeBatch(files, workflow, onProgress) {
        // 从设置中获取批处理配置
        const batchSize = this.store?.get('workflow.processing.batchSize', 100) || 100;
        const batchInterval = this.store?.get('workflow.processing.batchInterval', 100) || 100;
        const totalFiles = files.length;
        const totalBatches = Math.ceil(totalFiles / batchSize);
        console.log(`开始批处理执行工作流: ${workflow.name}, 总文件数: ${totalFiles}, 批大小: ${batchSize}, 总批次: ${totalBatches}`);
        const startTime = new Date().toISOString();
        const allStepResults = [];
        const allErrors = [];
        let processedCount = 0;
        // 重置创建的文件夹跟踪和counter映射
        this.createdDirectories.clear();
        this.counterMap.clear();
        this.dirScanCache.clear();
        for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
            const startIndex = batchIndex * batchSize;
            const endIndex = Math.min(startIndex + batchSize, totalFiles);
            const batchFiles = files.slice(startIndex, endIndex);
            console.log(`处理批次 ${batchIndex + 1}/${totalBatches}, 文件范围: ${startIndex}-${endIndex - 1}`);
            try {
                // 执行当前批次
                const batchResult = await this.execute(batchFiles, workflow);
                // 合并结果
                allStepResults.push(...batchResult.stepResults);
                allErrors.push(...batchResult.errors);
                processedCount += batchResult.processedFiles;
                // 报告进度
                if (onProgress) {
                    // 异步调用进度回调，避免阻塞批处理
                    setImmediate(() => {
                        onProgress({
                            processed: processedCount,
                            total: totalFiles,
                            currentBatch: batchIndex + 1,
                            totalBatches
                        });
                    });
                }
                // 批次间等待
                if (batchIndex < totalBatches - 1 && batchInterval > 0) {
                    await new Promise(resolve => setTimeout(resolve, batchInterval));
                }
            }
            catch (error) {
                console.error(`批次 ${batchIndex + 1} 执行失败:`, error);
                allErrors.push({
                    file: `批次 ${batchIndex + 1}`,
                    step: '批处理执行',
                    error: error instanceof Error ? error.message : String(error)
                });
            }
        }
        const endTime = new Date().toISOString();
        const duration = new Date(endTime).getTime() - new Date(startTime).getTime();
        return {
            workflowId: workflow.id,
            startTime,
            endTime,
            duration,
            totalFiles,
            processedFiles: processedCount,
            stepResults: allStepResults,
            errors: allErrors
        };
    }
    /**
     * 执行工作流
     */
    async execute(files, workflow) {
        // 重置中断状态
        this.isInterrupted = false;
        // 验证工作流配置
        const validation = this.validateWorkflowConfiguration(workflow);
        if (!validation.isValid) {
            throw new Error(`工作流配置错误:\n${validation.errors.join('\n')}`);
        }
        const startTime = new Date().toISOString();
        const stepResults = [];
        let currentFiles = [...files];
        const errors = [];
        // 初始化当前执行状态
        this.currentExecution = {
            workflowId: workflow.id,
            startTime,
            stepResults,
            processedFiles: 0,
            totalFiles: files.length,
            errors
        };
        // 重置创建的文件夹跟踪和counter映射
        this.createdDirectories.clear();
        this.processedDirectories.clear();
        this.cleanedEmptyDirectories.clear();
        this.counterMap.clear();
        // 清理目录扫描缓存
        this.dirScanCache.clear();
        try {
            for (const step of workflow.steps.filter(s => s.enabled).sort((a, b) => a.order - b.order)) {
                // 检查是否被中断
                if (this.isInterrupted) {
                    console.warn(`[工作流引擎] 在步骤"${step.name}"前检测到中断信号，停止执行`);
                    errors.push({
                        file: '',
                        error: this.t('workflow.interrupted', { stepName: step.name }),
                        step: step.id
                    });
                    break;
                }
                const stepStart = Date.now();
                // 获取步骤的输入文件
                const inputFiles = await this.getStepInputFiles(currentFiles, step, stepResults);
                // 执行步骤
                const { outputFiles, stepErrors } = await this.processStepExecute(inputFiles, step);
                const stepResult = {
                    stepId: step.id,
                    stepName: step.name,
                    inputFiles,
                    outputFiles,
                    processedCount: outputFiles.length,
                    errors: stepErrors,
                    duration: Date.now() - stepStart
                };
                stepResults.push(stepResult);
                errors.push(...stepErrors);
                // 更新当前执行状态
                if (this.currentExecution) {
                    this.currentExecution.stepResults = [...stepResults];
                    this.currentExecution.processedFiles = stepResults.reduce((total, sr) => total + sr.processedCount, 0);
                    this.currentExecution.errors = [...errors];
                }
                // 更新当前文件列表为步骤输出
                currentFiles = outputFiles;
                // 再次检查中断状态（步骤执行后）
                if (this.isInterrupted) {
                    console.warn(`[工作流引擎] 在步骤"${step.name}"后检测到中断信号，停止执行`);
                    errors.push({
                        file: '',
                        error: `工作流在步骤"${step.name}"后被中断`,
                        step: step.id
                    });
                    break;
                }
            }
        }
        catch (error) {
            // 捕获执行过程中的异常
            const errorMessage = error instanceof Error ? error.message : String(error);
            console.error('[工作流引擎] 执行过程中发生异常:', errorMessage);
            errors.push({
                file: '',
                error: `执行异常: ${errorMessage}`,
                step: 'system'
            });
            // 保存部分结果
            const partialResult = await this.savePartialResult();
            if (partialResult) {
                // 清理执行状态
                this.currentExecution = null;
                return partialResult;
            }
        }
        const endTime = new Date().toISOString();
        // 默认清理机制：始终清理工作流创建的空文件夹（软件内部清理）
        try {
            await this.cleanupCreatedEmptyDirectories();
            console.log(`✅ 已清理工作流创建的空文件夹`);
        }
        catch (cleanupError) {
            console.warn('清理工作流创建的空文件夹时出错:', cleanupError);
            // 不影响主要的工作流结果
        }
        // 用户功能：如果启用，清理处理过程中遇到的所有空文件夹
        if (workflow.cleanupEmptyFolders === true) {
            try {
                await this.cleanupAllProcessedEmptyDirectories();
                console.log(`✅ 已清理处理过程中的所有空文件夹`);
            }
            catch (cleanupError) {
                console.warn('清理处理过程中的空文件夹时出错:', cleanupError);
                // 不影响主要的工作流结果
            }
        }
        // 计算实际处理的文件数量（所有步骤中实际被处理的文件总数）
        const totalProcessedFiles = stepResults.reduce((total, stepResult) => {
            // 计算该步骤中实际被处理的文件数量
            // 只要文件状态为success且有newPath字段，就算作已处理
            const processedInStep = stepResult.outputFiles.filter(file => file.status === 'success' && file.newPath).length;
            return total + processedInStep;
        }, 0);
        // 清理执行状态
        this.currentExecution = null;
        return {
            workflowId: workflow.id,
            stepResults,
            totalFiles: files.length,
            processedFiles: totalProcessedFiles,
            errors,
            startTime,
            endTime,
            duration: Date.parse(endTime) - Date.parse(startTime)
        };
    }
    /**
     * 获取步骤的输入文件
     */
    async getStepInputFiles(currentFiles, step, stepResults) {
        let inputFiles = [];
        switch (step.inputSource.type) {
            case 'original':
                // 重新扫描原始目录，获取所有文件和文件夹
                try {
                    // 获取步骤的子文件夹处理配置
                    const processSubfolders = step.actions.length > 0 ? (step.actions[0].config?.processSubfolders ?? true) : true;
                    const maxDepth = step.actions.length > 0 ? (step.actions[0].config?.maxDepth ?? -1) : -1;
                    // 重新扫描时不进行processTarget过滤，获取所有文件和文件夹
                    inputFiles = await this.rescanOriginalDirectories(currentFiles, 'both', processSubfolders, maxDepth);
                    console.log(`从原始目录重新扫描到 ${inputFiles.length} 个项目`);
                }
                catch (error) {
                    console.error('重新扫描原始目录失败，使用缓存的文件列表', error);
                    inputFiles = currentFiles;
                }
                break;
            case 'previous_step':
                if (step.inputSource.stepId) {
                    const previousStep = stepResults.find(sr => sr.stepId === step.inputSource.stepId);
                    inputFiles = previousStep ? previousStep.outputFiles : [];
                }
                else {
                    inputFiles = stepResults.length > 0 ? stepResults[stepResults.length - 1].outputFiles : currentFiles;
                }
                break;
            case 'specific_path':
                if (step.inputSource.path) {
                    try {
                        // 获取步骤的子文件夹处理配置
                        const processSubfolders = step.actions.length > 0 ? (step.actions[0].config?.processSubfolders ?? true) : true;
                        const maxDepth = step.actions.length > 0 ? (step.actions[0].config?.maxDepth ?? -1) : -1;
                        inputFiles = await this.loadItemsFromPath(step.inputSource.path, step.processTarget || 'files', processSubfolders, maxDepth);
                        console.log(`从指定路径加载文件: ${step.inputSource.path}, 找到 ${inputFiles.length} 个项目`);
                        // 确保从指定路径加载的文件也使用一致的ID生成机制
                        inputFiles = inputFiles.map(file => ({
                            ...file,
                            id: this.generateFileId(file.path)
                        }));
                        // specific_path已经在loadItemsFromPath中进行了processTarget过滤，直接返回
                        return inputFiles;
                    }
                    catch (error) {
                        console.error(`从指定路径加载文件失败: ${step.inputSource.path}`, error);
                        throw new Error(`无法从指定路径加载文件: ${step.inputSource.path} - ${error instanceof Error ? error.message : String(error)}`);
                    }
                }
                else {
                    console.warn('特定路径输入源未配置路径，使用当前文件');
                    inputFiles = currentFiles;
                }
                break;
            default:
                inputFiles = currentFiles;
                break;
        }
        // 根据processTarget过滤输入文件（除了specific_path，它已经过滤过了）
        return this.filterFilesByProcessTarget(inputFiles, step.processTarget || 'files');
    }
    /**
     * 根据处理目标过滤文件
     */
    filterFilesByProcessTarget(files, processTarget) {
        switch (processTarget) {
            case 'files':
                return files.filter(file => !file.isDirectory);
            case 'folders':
                return files.filter(file => file.isDirectory);
            default:
                return files.filter(file => !file.isDirectory); // 默认只处理文件
        }
    }
    /**
     * 从指定路径加载文件或文件夹
     */
    async loadItemsFromPath(targetPath, processTarget = 'files', processSubfolders = true, maxDepth = -1) {
        // 验证路径安全性
        if (!this.validatePath(targetPath)) {
            throw new Error(`路径不安全或无效: ${targetPath}`);
        }
        // 检查路径是否存在
        if (!await fs_extra_1.default.pathExists(targetPath)) {
            throw new Error(`路径不存在: ${targetPath}`);
        }
        try {
            const stat = await fs_extra_1.default.stat(targetPath);
            const items = [];
            if (stat.isDirectory()) {
                // 检查目录权限
                try {
                    await fs_extra_1.default.access(targetPath, fs_extra_1.default.constants.R_OK);
                }
                catch (error) {
                    throw new Error(`没有读取目录的权限: ${targetPath}`);
                }
                // 如果是目录，根据处理目标获取相应的项目
                const itemPaths = await this.getAllItemsInDirectory(targetPath, undefined, processTarget, processSubfolders, maxDepth);
                console.log(`从目录 ${targetPath} 扫描到 ${itemPaths.length} 个项目`);
                for (const itemPath of itemPaths) {
                    try {
                        const itemInfo = await this.getItemInfo(itemPath);
                        items.push(itemInfo);
                    }
                    catch (itemError) {
                        console.warn(`跳过无法读取的项目: ${itemPath}`, itemError);
                        // 继续处理其他项目，不中断整个流程
                    }
                }
            }
            else {
                // 如果是单个文件，根据处理目标决定是否包含
                if (processTarget === 'files') {
                    const itemInfo = await this.getItemInfo(targetPath);
                    items.push(itemInfo);
                    console.log(`加载单个文件: ${targetPath}`);
                }
                else {
                    console.log(`跳过文件（处理目标为文件夹）: ${targetPath}`);
                }
            }
            console.log(`从路径 ${targetPath} 成功加载 ${items.length} 个项目`);
            return items;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            console.error(`从路径加载项目失败: ${targetPath}`, error);
            throw new Error(`加载失败: ${errorMessage}`);
        }
    }
    /**
     * 从指定路径加载文件（保持向后兼容）
     */
    async loadFilesFromPath(targetPath) {
        return this.loadItemsFromPath(targetPath, 'files', true, -1);
    }
    /**
     * 递归获取目录中的所有项目（文件和/或文件夹）
     */
    async getAllItemsInDirectory(dirPath, maxItems, processTarget = 'files', processSubfolders = true, maxDepth = -1) {
        const items = [];
        async function processDirectory(currentPath, currentDepth = 0) {
            if (maxItems && items.length >= maxItems) {
                return;
            }
            // 检查深度限制：maxDepth=N表示处理到深度N-1
            // 例如：maxDepth=1表示只处理根目录(depth=0)，maxDepth=2表示处理根目录+1级子文件夹(depth=0,1)
            if (maxDepth !== -1 && currentDepth >= maxDepth) {
                return;
            }
            try {
                const dirItems = await fs_extra_1.default.readdir(currentPath);
                // 对文件夹内容进行排序，确保处理顺序一致
                const sortedItems = dirItems.sort((a, b) => {
                    return a.localeCompare(b, undefined, {
                        numeric: true,
                        sensitivity: 'base'
                    });
                });
                for (const item of sortedItems) {
                    if (maxItems && items.length >= maxItems) {
                        break;
                    }
                    const fullPath = path_1.default.join(currentPath, item);
                    try {
                        const stat = await fs_extra_1.default.stat(fullPath);
                        if (stat.isDirectory()) {
                            // 根据处理目标决定是否添加文件夹
                            if (processTarget === 'folders' || processTarget === 'both') {
                                items.push(fullPath);
                            }
                            // 只有在处理子文件夹时才递归处理子目录
                            if (processSubfolders) {
                                await processDirectory(fullPath, currentDepth + 1);
                            }
                        }
                        else {
                            // 根据处理目标决定是否添加文件
                            if (processTarget === 'files' || processTarget === 'both') {
                                items.push(fullPath);
                            }
                        }
                    }
                    catch (error) {
                        console.warn(`Error processing ${fullPath}:`, error);
                    }
                }
            }
            catch (error) {
                console.error(`Error reading directory ${currentPath}:`, error);
            }
        }
        await processDirectory(dirPath, 0);
        // 对于文件夹处理，需要按深度排序以避免移动冲突
        // 深度越深的文件夹越先处理，这样不会影响父文件夹的移动
        if (processTarget === 'folders' && items.length > 1) {
            return items.sort((a, b) => {
                const depthA = a.split(path_1.default.sep).length;
                const depthB = b.split(path_1.default.sep).length;
                // 深度深的排在前面（先处理），如果深度相同则按字母顺序
                if (depthA !== depthB) {
                    return depthB - depthA;
                }
                return a.localeCompare(b, undefined, {
                    numeric: true,
                    sensitivity: 'base'
                });
            });
        }
        return items.sort((a, b) => {
            return a.localeCompare(b, undefined, {
                numeric: true,
                sensitivity: 'base'
            });
        });
    }
    /**
     * 递归获取目录中的所有文件（保持向后兼容）
     */
    async getAllFilesInDirectory(dirPath, maxFiles) {
        return this.getAllItemsInDirectory(dirPath, maxFiles, 'files', true, -1);
    }
    /**
     * 检查文件或文件夹权限
     */
    async checkPermissions(itemPath, operation = 'both') {
        try {
            let mode = fs_extra_1.default.constants.F_OK; // 检查存在性
            if (operation === 'read' || operation === 'both') {
                mode |= fs_extra_1.default.constants.R_OK;
            }
            if (operation === 'write' || operation === 'both') {
                mode |= fs_extra_1.default.constants.W_OK;
            }
            await fs_extra_1.default.access(itemPath, mode);
            return true;
        }
        catch (error) {
            console.warn(`Permission denied for ${operation} operation on: ${itemPath}`);
            return false;
        }
    }
    /**
     * 验证操作的安全性和可行性
     */
    async validateOperation(sourcePath, targetPath, operation) {
        // 检查源路径存在
        if (!(await fs_extra_1.default.pathExists(sourcePath))) {
            throw new Error(`源路径不存在: ${sourcePath}`);
        }
        // 检查源路径权限
        if (!(await this.checkPermissions(sourcePath, 'read'))) {
            throw new Error(`没有读取权限: ${sourcePath}`);
        }
        // 对于移动和复制操作，检查目标路径
        if (operation === 'move' || operation === 'copy') {
            const targetDir = path_1.default.dirname(targetPath);
            // 检查目标目录权限
            if (await fs_extra_1.default.pathExists(targetDir)) {
                if (!(await this.checkPermissions(targetDir, 'write'))) {
                    throw new Error(`目标目录没有写入权限: ${targetDir}`);
                }
            }
            // 检查目标路径不是源路径的子路径（避免移动到自己内部）
            const normalizedSource = path_1.default.normalize(sourcePath);
            const normalizedTarget = path_1.default.normalize(targetPath);
            if (normalizedTarget.startsWith(normalizedSource + path_1.default.sep)) {
                throw new Error(`不能将文件夹移动到自己内部: ${sourcePath} -> ${targetPath}`);
            }
        }
        // 对于删除操作，检查写入权限
        if (operation === 'delete') {
            const parentDir = path_1.default.dirname(sourcePath);
            if (!(await this.checkPermissions(parentDir, 'write'))) {
                throw new Error(`没有删除权限: ${sourcePath}`);
            }
        }
    }
    /**
     * 检查磁盘空间是否足够
     */
    async checkDiskSpace(targetPath, requiredSize) {
        try {
            const targetDir = require('path').dirname(targetPath);
            // 简化的磁盘空间检查
            if (requiredSize > 0) {
                // 启发式检查：如果需要的空间超过 10GB，给出警告
                const maxSafeSize = 10 * 1024 * 1024 * 1024; // 10GB
                if (requiredSize > maxSafeSize) {
                    return {
                        hasSpace: false,
                        error: `文件过大 (${Math.round(requiredSize / 1024 / 1024)}MB)，可能导致磁盘空间不足`
                    };
                }
            }
            return { hasSpace: true };
        }
        catch (error) {
            const errorMsg = error instanceof Error ? error.message : String(error);
            return { hasSpace: false, error: `磁盘空间检查失败: ${errorMsg}` };
        }
    }
    /**
     * 分类和翻译错误信息
     */
    categorizeError(error, operation, path) {
        const errorMessage = error.message.toLowerCase();
        if (errorMessage.includes('permission') || errorMessage.includes('access') || error.message.includes('EACCES')) {
            return this.t('error.permissionDenied', { operation, path });
        }
        if (errorMessage.includes('not found') || errorMessage.includes('enoent')) {
            return this.t('error.pathNotFound', { path });
        }
        if (errorMessage.includes('directory not empty') || errorMessage.includes('enotempty')) {
            return this.t('error.directoryNotEmpty', { path });
        }
        if (errorMessage.includes('file exists') || errorMessage.includes('eexist')) {
            return this.t('error.targetExists', { path });
        }
        if (errorMessage.includes('cross-device') || errorMessage.includes('exdev')) {
            return this.t('error.crossDevice', { path });
        }
        if (errorMessage.includes('name too long') || errorMessage.includes('enametoolong')) {
            return this.t('error.pathTooLong', { path });
        }
        if (errorMessage.includes('no space') || errorMessage.includes('enospc')) {
            return this.t('error.diskFull', { path });
        }
        // 默认错误信息
        return this.t('error.generic', { path, error: error.message });
    }
    /**
     * 统一的深度计算方法
     */
    calculateDepth(itemPath, basePath) {
        const normalizedPath = path_1.default.normalize(itemPath);
        const parts = normalizedPath.split(path_1.default.sep).filter(part => part.length > 0);
        if (basePath) {
            const normalizedBasePath = path_1.default.normalize(basePath);
            const baseParts = normalizedBasePath.split(path_1.default.sep).filter(part => part.length > 0);
            return Math.max(0, parts.length - baseParts.length);
        }
        return parts.length;
    }
    /**
     * 检查文件夹是否逻辑上为空（包括只包含空子文件夹的情况）
     */
    async isLogicallyEmpty(folderPath, visitedPaths = new Set()) {
        // 防止循环引用
        const realPath = await fs_extra_1.default.realpath(folderPath).catch(() => folderPath);
        if (visitedPaths.has(realPath)) {
            return true; // 循环引用的情况视为空
        }
        visitedPaths.add(realPath);
        try {
            const items = await fs_extra_1.default.readdir(folderPath);
            for (const item of items) {
                const itemPath = path_1.default.join(folderPath, item);
                try {
                    const itemStat = await fs_extra_1.default.lstat(itemPath);
                    if (itemStat.isSymbolicLink()) {
                        // 跳过符号链接
                        continue;
                    }
                    else if (!itemStat.isDirectory()) {
                        // 有文件，不为空
                        visitedPaths.delete(realPath);
                        return false;
                    }
                    else {
                        // 递归检查子文件夹
                        if (!(await this.isLogicallyEmpty(itemPath, visitedPaths))) {
                            visitedPaths.delete(realPath);
                            return false;
                        }
                    }
                }
                catch (error) {
                    console.warn(`Error checking item ${itemPath}:`, error);
                    // 出错的项目视为非空，保守处理
                    visitedPaths.delete(realPath);
                    return false;
                }
            }
            visitedPaths.delete(realPath);
            return true; // 所有子项都是空文件夹或不存在
        }
        catch (error) {
            console.warn(`Error reading directory ${folderPath}:`, error);
            visitedPaths.delete(realPath);
            return false; // 无法读取时视为非空
        }
    }
    /**
     * 递归计算文件夹大小的辅助函数
     */
    async calculateFolderSize(folderPath, visitedPaths = new Set()) {
        // 防止循环引用
        const realPath = await fs_extra_1.default.realpath(folderPath).catch(() => folderPath);
        if (visitedPaths.has(realPath)) {
            console.warn(`Circular reference detected: ${folderPath}`);
            return { size: 0, fileCount: 0, folderCount: 0 };
        }
        visitedPaths.add(realPath);
        let totalSize = 0;
        let totalFileCount = 0;
        let totalFolderCount = 0;
        try {
            const items = await fs_extra_1.default.readdir(folderPath);
            for (const item of items) {
                const itemPath = path_1.default.join(folderPath, item);
                try {
                    const itemStat = await fs_extra_1.default.lstat(itemPath); // 使用lstat避免跟随符号链接
                    if (itemStat.isSymbolicLink()) {
                        // 跳过符号链接，避免循环引用和重复计算
                        continue;
                    }
                    else if (itemStat.isDirectory()) {
                        totalFolderCount++;
                        const subResult = await this.calculateFolderSize(itemPath, visitedPaths);
                        totalSize += subResult.size;
                        totalFileCount += subResult.fileCount;
                        totalFolderCount += subResult.folderCount;
                    }
                    else {
                        totalFileCount++;
                        totalSize += itemStat.size;
                    }
                }
                catch (error) {
                    console.warn(`Error processing item ${itemPath}:`, error);
                }
            }
        }
        catch (error) {
            console.warn(`Error reading directory ${folderPath}:`, error);
        }
        visitedPaths.delete(realPath);
        return { size: totalSize, fileCount: totalFileCount, folderCount: totalFolderCount };
    }
    /**
     * 获取文件或文件夹的详细信息
     */
    async getItemInfo(itemPath, originalDir) {
        try {
            const stat = await fs_extra_1.default.lstat(itemPath); // 使用lstat获取符号链接信息
            const isDirectory = stat.isDirectory();
            const parsedPath = path_1.default.parse(itemPath);
            let fileCount = 0;
            let folderCount = 0;
            let totalSize = stat.size;
            let isEmpty = false;
            if (isDirectory) {
                try {
                    const items = await fs_extra_1.default.readdir(itemPath);
                    const physicallyEmpty = items.length === 0;
                    if (physicallyEmpty) {
                        isEmpty = true;
                    }
                    else {
                        // 递归计算文件夹大小和统计信息
                        const folderStats = await this.calculateFolderSize(itemPath);
                        totalSize = folderStats.size;
                        fileCount = folderStats.fileCount;
                        folderCount = folderStats.folderCount;
                        // 检查是否逻辑上为空（只包含空子文件夹）
                        isEmpty = await this.isLogicallyEmpty(itemPath);
                    }
                }
                catch (error) {
                    console.warn(`Error reading directory ${itemPath}:`, error);
                }
            }
            return {
                id: `item_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
                name: parsedPath.base,
                path: itemPath,
                size: stat.size,
                type: isDirectory ? 'folder' : (parsedPath.ext.slice(1).toLowerCase() || 'file'),
                status: 'pending',
                createdDate: stat.birthtime.toISOString(),
                modifiedDate: stat.mtime.toISOString(),
                isDirectory,
                originalDir: originalDir || (isDirectory ? itemPath : path_1.default.dirname(itemPath)), // 记录原始目录
                fileCount: isDirectory ? fileCount : undefined,
                folderCount: isDirectory ? folderCount : undefined,
                totalSize: isDirectory ? totalSize : undefined,
                isEmpty: isDirectory ? isEmpty : undefined,
                depth: this.calculateDepth(itemPath)
            };
        }
        catch (error) {
            console.error(`Error getting item info for ${itemPath}:`, error);
            const parsedPath = path_1.default.parse(itemPath);
            return {
                id: `item_${(0, uuid_1.v4)()}`,
                name: parsedPath.base,
                path: itemPath,
                size: 0,
                type: parsedPath.ext.slice(1).toLowerCase() || 'unknown',
                status: 'error',
                error: 'Failed to get item info',
                createdDate: new Date().toISOString(),
                isDirectory: false,
                originalDir: originalDir || path_1.default.dirname(itemPath) // 记录原始目录
            };
        }
    }
    /**
     * 生成统一的文件ID
     */
    generateFileId(filePath) {
        // 使用文件路径的哈希值确保同一文件始终有相同的ID
        const crypto = require('crypto');
        const hash = crypto.createHash('md5').update(filePath).digest('hex');
        return `file_${hash.substring(0, 16)}`;
    }
    /**
     * 获取文件信息
     */
    async getFileInfo(filePath) {
        try {
            const stat = await fs_extra_1.default.stat(filePath);
            const parsedPath = path_1.default.parse(filePath);
            return {
                id: this.generateFileId(filePath),
                name: parsedPath.base,
                path: filePath,
                size: stat.size,
                type: parsedPath.ext.slice(1).toLowerCase(),
                status: 'pending',
                createdDate: stat.birthtime.toISOString(),
                modifiedDate: stat.mtime.toISOString()
            };
        }
        catch (error) {
            console.error(`Error getting file info for ${filePath}:`, error);
            const parsedPath = path_1.default.parse(filePath);
            return {
                id: this.generateFileId(filePath),
                name: parsedPath.base,
                path: filePath,
                size: 0,
                type: parsedPath.ext.slice(1).toLowerCase(),
                status: 'error',
                error: 'Failed to get file info',
                createdDate: new Date().toISOString()
            };
        }
    }
    /**
     * 预览步骤处理
     */
    async processStepPreview(files, step) {
        const outputFiles = [];
        const stepErrors = [];
        let fileIndex = 0; // 用于counter递增预览
        let hasMatches = false; // 跟踪是否有文件匹配
        for (const file of files) {
            try {
                // 检查文件是否匹配条件
                if (this.matchesConditions(file, step.conditions)) {
                    hasMatches = true;
                    // 计算新路径
                    const newPath = await this.calculateNewPath(file.path, step.actions[0], fileIndex, file);
                    fileIndex++; // 只有匹配条件的文件才递增索引
                    // 保持文件ID不变，只更新路径和状态
                    outputFiles.push({
                        ...file,
                        newPath,
                        status: 'pending',
                        // 保留原始ID，确保在多步骤中可以正确匹配
                        id: file.id
                    });
                }
                else {
                    // 不匹配条件的文件保持原样，包括ID
                    outputFiles.push({
                        ...file,
                        id: file.id
                    });
                }
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                const translatedError = this.translateError(errorMessage);
                stepErrors.push({
                    file: file.path,
                    error: translatedError,
                    step: step.id
                });
                // 预览模式下也要将错误文件添加到输出中，保持与执行模式一致
                outputFiles.push({
                    ...file,
                    status: 'error',
                    error: translatedError,
                    // 保留原始ID
                    id: file.id
                });
            }
        }
        return { outputFiles, stepErrors, hasMatches };
    }
    /**
     * 执行步骤处理
     */
    async processStepExecute(files, step) {
        const outputFiles = [];
        const stepErrors = [];
        for (const file of files) {
            try {
                // 跳过已经出错的文件，避免在后续步骤中继续处理
                if (file.status === 'error') {
                    outputFiles.push({
                        ...file,
                        id: file.id // 保持ID不变
                    });
                    continue;
                }
                // 检查是否为空文件夹，如果是则跟踪但跳过处理
                if (file.type === 'folder' && await this.isEmptyDirectory(file.path)) {
                    this.processedDirectories.add(file.path);
                    console.log(`跳过空文件夹: ${file.path}`);
                    // 跳过空文件夹的处理，不添加到输出文件中
                    continue;
                }
                // 检查文件是否匹配条件
                if (this.matchesConditions(file, step.conditions)) {
                    // 执行动作
                    const newPath = await this.executeActions(file.path, step.actions);
                    outputFiles.push({
                        ...file,
                        path: newPath,
                        newPath,
                        status: 'success',
                        // 保持原始ID不变
                        id: file.id
                    });
                }
                else {
                    // 不匹配条件的文件保持原样，包括ID
                    outputFiles.push({
                        ...file,
                        id: file.id
                    });
                }
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                const translatedError = this.translateError(errorMessage);
                stepErrors.push({
                    file: file.path,
                    error: translatedError,
                    step: step.id
                });
                outputFiles.push({
                    ...file,
                    status: 'error',
                    error: translatedError,
                    // 保持原始ID不变
                    id: file.id
                });
            }
        }
        return { outputFiles, stepErrors };
    }
    /**
     * 检查文件是否匹配条件组
     */
    matchesConditions(file, conditionGroup) {
        if (conditionGroup.conditions.length === 0) {
            return true; // 无条件匹配所有文件
        }
        // 检查直接条件
        const conditionResults = conditionGroup.conditions
            .filter(condition => condition.enabled)
            .map(condition => this.matchesCondition(file, condition));
        // 检查嵌套条件组
        const groupResults = (conditionGroup.groups || [])
            .map(group => this.matchesConditions(file, group));
        // 合并所有结果
        const allResults = [...conditionResults, ...groupResults];
        if (allResults.length === 0) {
            return true; // 没有条件时匹配所有文件
        }
        return conditionGroup.operator === 'AND'
            ? allResults.every(r => r)
            : allResults.some(r => r);
    }
    /**
     * 检查文件是否匹配单个条件
     */
    matchesCondition(file, condition) {
        const fileValue = this.getFileValue(file, condition.field);
        let conditionValue = condition.value;
        // 检查是否为日期字段
        const isDateField = condition.field === 'createdDate' || condition.field === 'modifiedDate';
        // 如果是相对日期，计算实际的日期值
        if (isDateField && condition.dateType === 'relative') {
            conditionValue = this.calculateRelativeDate(condition);
        }
        switch (condition.operator) {
            case 'equals':
                if (isDateField) {
                    // 日期比较：只比较日期部分，忽略时间
                    const fileDate = this.getDateOnly(String(fileValue));
                    const conditionDate = this.getDateOnly(String(conditionValue));
                    return fileDate === conditionDate;
                }
                // 文件大小比较：需要考虑单位转换
                if (condition.field === 'fileSize' || condition.field === 'folderSize') {
                    const fileValueInBytes = Number(fileValue);
                    const conditionValueInBytes = this.convertToBytes(Number(conditionValue), condition.sizeUnit || 'MB');
                    return fileValueInBytes === conditionValueInBytes;
                }
                return fileValue === conditionValue;
            case 'is':
                // 专门用于布尔值比较
                return fileValue === conditionValue;
            case 'notEquals':
                if (isDateField) {
                    // 日期比较：只比较日期部分，忽略时间
                    const fileDate = this.getDateOnly(String(fileValue));
                    const conditionDate = this.getDateOnly(String(conditionValue));
                    return fileDate !== conditionDate;
                }
                // 文件大小比较：需要考虑单位转换
                if (condition.field === 'fileSize' || condition.field === 'folderSize') {
                    const fileValueInBytes = Number(fileValue);
                    const conditionValueInBytes = this.convertToBytes(Number(conditionValue), condition.sizeUnit || 'MB');
                    return fileValueInBytes !== conditionValueInBytes;
                }
                return fileValue !== conditionValue;
            case 'contains':
                return String(fileValue).toLowerCase().includes(String(conditionValue).toLowerCase());
            case 'notContains':
                return !String(fileValue).toLowerCase().includes(String(conditionValue).toLowerCase());
            case 'startsWith':
                return String(fileValue).toLowerCase().startsWith(String(conditionValue).toLowerCase());
            case 'notStartsWith':
                return !String(fileValue).toLowerCase().startsWith(String(conditionValue).toLowerCase());
            case 'endsWith':
                return String(fileValue).toLowerCase().endsWith(String(conditionValue).toLowerCase());
            case 'notEndsWith':
                return !String(fileValue).toLowerCase().endsWith(String(conditionValue).toLowerCase());
            case 'greaterThan':
                if (isDateField) {
                    // 相对日期的特殊处理
                    if (condition.dateType === 'relative') {
                        return this.matchesRelativeDate(String(fileValue), condition, 'greaterThan');
                    }
                    // 日期比较：文件日期晚于条件日期
                    return this.compareDates(String(fileValue), String(conditionValue)) > 0;
                }
                // 文件大小比较：需要考虑单位转换
                if (condition.field === 'fileSize' || condition.field === 'folderSize') {
                    const fileValueInBytes = Number(fileValue);
                    const conditionValueInBytes = this.convertToBytes(Number(conditionValue), condition.sizeUnit || 'MB');
                    return fileValueInBytes > conditionValueInBytes;
                }
                return Number(fileValue) > Number(conditionValue);
            case 'lessThan':
                if (isDateField) {
                    // 相对日期的特殊处理
                    if (condition.dateType === 'relative') {
                        return this.matchesRelativeDate(String(fileValue), condition, 'lessThan');
                    }
                    // 日期比较：文件日期早于条件日期
                    return this.compareDates(String(fileValue), String(conditionValue)) < 0;
                }
                // 文件大小比较：需要考虑单位转换
                if (condition.field === 'fileSize' || condition.field === 'folderSize') {
                    const fileValueInBytes = Number(fileValue);
                    const conditionValueInBytes = this.convertToBytes(Number(conditionValue), condition.sizeUnit || 'MB');
                    return fileValueInBytes < conditionValueInBytes;
                }
                return Number(fileValue) < Number(conditionValue);
            case 'greaterThanOrEqual':
                if (isDateField) {
                    // 相对日期的特殊处理
                    if (condition.dateType === 'relative') {
                        return this.matchesRelativeDate(String(fileValue), condition, 'greaterThanOrEqual');
                    }
                    // 日期比较：文件日期不早于条件日期
                    return this.compareDates(String(fileValue), String(conditionValue)) >= 0;
                }
                // 文件大小比较：需要考虑单位转换
                if (condition.field === 'fileSize' || condition.field === 'folderSize') {
                    const fileValueInBytes = Number(fileValue);
                    const conditionValueInBytes = this.convertToBytes(Number(conditionValue), condition.sizeUnit || 'MB');
                    return fileValueInBytes >= conditionValueInBytes;
                }
                return Number(fileValue) >= Number(conditionValue);
            case 'lessThanOrEqual':
                if (isDateField) {
                    // 相对日期的特殊处理
                    if (condition.dateType === 'relative') {
                        return this.matchesRelativeDate(String(fileValue), condition, 'lessThanOrEqual');
                    }
                    // 日期比较：文件日期不晚于条件日期
                    return this.compareDates(String(fileValue), String(conditionValue)) <= 0;
                }
                // 文件大小比较：需要考虑单位转换
                if (condition.field === 'fileSize' || condition.field === 'folderSize') {
                    const fileValueInBytes = Number(fileValue);
                    const conditionValueInBytes = this.convertToBytes(Number(conditionValue), condition.sizeUnit || 'MB');
                    return fileValueInBytes <= conditionValueInBytes;
                }
                return Number(fileValue) <= Number(conditionValue);
            case 'in':
                return Array.isArray(conditionValue) && conditionValue.includes(String(fileValue));
            case 'notIn':
                return Array.isArray(conditionValue) && !conditionValue.includes(String(fileValue));
            case 'regex':
                try {
                    const regex = new RegExp(String(conditionValue), 'i');
                    return regex.test(String(fileValue));
                }
                catch {
                    return false;
                }
            default:
                return false;
        }
    }
    /**
     * 比较两个日期字符串
     * @param dateStr1 第一个日期字符串
     * @param dateStr2 第二个日期字符串
     * @returns 1 如果 dateStr1 > dateStr2, -1 如果 dateStr1 < dateStr2, 0 如果相等
     */
    compareDates(dateStr1, dateStr2) {
        try {
            const date1 = new Date(dateStr1);
            const date2 = new Date(dateStr2);
            // 检查日期是否有效
            if (isNaN(date1.getTime()) || isNaN(date2.getTime())) {
                console.warn(`无效的日期格式: ${dateStr1} 或 ${dateStr2}`);
                return 0;
            }
            // 使用UTC时间只比较日期部分，避免时区问题
            const dateOnly1 = new Date(Date.UTC(date1.getUTCFullYear(), date1.getUTCMonth(), date1.getUTCDate()));
            const dateOnly2 = new Date(Date.UTC(date2.getUTCFullYear(), date2.getUTCMonth(), date2.getUTCDate()));
            const time1 = dateOnly1.getTime();
            const time2 = dateOnly2.getTime();
            if (time1 > time2)
                return 1;
            if (time1 < time2)
                return -1;
            return 0;
        }
        catch (error) {
            console.warn(`日期比较失败: ${dateStr1} vs ${dateStr2}`, error);
            return 0;
        }
    }
    /**
     * 获取日期字符串的日期部分（YYYY-MM-DD格式）
     * @param dateStr 日期字符串
     * @returns YYYY-MM-DD 格式的日期字符串
     */
    getDateOnly(dateStr) {
        try {
            const date = new Date(dateStr);
            if (isNaN(date.getTime())) {
                return dateStr; // 如果不是有效日期，返回原字符串
            }
            // 使用UTC时间避免时区问题
            const year = date.getUTCFullYear();
            const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
            const day = date.getUTCDate().toString().padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        catch (error) {
            console.warn(`获取日期部分失败: ${dateStr}`, error);
            return dateStr;
        }
    }
    /**
     * 根据分类方式生成子文件夹路径
     */
    async generateClassificationPath(filePath, config) {
        const classifyBy = config.classifyBy ||
            (config.createSubfolders ? 'fileType' : null) ||
            (config.preserveFolderStructure ? 'preserveStructure' : null);
        if (!classifyBy || classifyBy === 'none') {
            return '';
        }
        const fileInfo = path_1.default.parse(filePath);
        switch (classifyBy) {
            case 'fileType':
                return this.getFileTypeCategory(fileInfo.ext.slice(1));
            case 'createdDate':
            case 'modifiedDate':
                try {
                    const stat = await fs_extra_1.default.stat(filePath);
                    const date = classifyBy === 'createdDate' ? stat.birthtime : stat.mtime;
                    return this.generateDateFolderPath(date, config.dateGrouping || 'yearMonth');
                }
                catch (error) {
                    console.warn(`无法获取文件日期: ${filePath}`, error);
                    return '未知日期';
                }
            case 'fileSize':
                try {
                    const stat = await fs_extra_1.default.stat(filePath);
                    return this.generateSizeFolderPath(stat.size, config);
                }
                catch (error) {
                    console.warn(`无法获取文件大小: ${filePath}`, error);
                    return '未知大小';
                }
            case 'extension':
                const ext = fileInfo.ext.slice(1).toLowerCase();
                return ext || '无扩展名';
            case 'preserveStructure':
                // 保持文件夹结构：返回空字符串，由preserveFolderStructure逻辑处理
                return '';
            default:
                return '';
        }
    }
    /**
     * 生成日期文件夹路径
     */
    generateDateFolderPath(date, grouping) {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const quarter = Math.ceil((date.getMonth() + 1) / 3);
        const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'];
        switch (grouping) {
            case 'year':
                return year.toString();
            case 'yearMonth':
                return path_1.default.join(year.toString(), month);
            case 'yearMonthDay':
                return path_1.default.join(year.toString(), month, day);
            case 'quarter':
                return path_1.default.join(year.toString(), `Q${quarter}`);
            case 'monthName':
                return path_1.default.join(year.toString(), monthNames[date.getMonth()]);
            default:
                return path_1.default.join(year.toString(), month);
        }
    }
    /**
     * 生成文件大小文件夹路径
     */
    generateSizeFolderPath(sizeBytes, config) {
        const sizeMB = sizeBytes / (1024 * 1024);
        const sizeGB = sizeMB / 1024;
        // 如果是自定义范围
        if (config.sizeClassifyMode === 'custom' && config.customSizeRanges) {
            for (const range of config.customSizeRanges) {
                const minBytes = this.convertToBytes(range.minSize, range.unit);
                const maxBytes = range.maxSize === -1 ? Infinity : this.convertToBytes(range.maxSize, range.unit);
                if (sizeBytes >= minBytes && sizeBytes <= maxBytes) {
                    return range.folderName;
                }
            }
            return '其他大小';
        }
        // 使用预设范围
        const preset = config.sizePreset || 'general';
        switch (preset) {
            case 'general':
                if (sizeMB < 1)
                    return '小文件';
                if (sizeMB < 100)
                    return '中等文件';
                if (sizeGB < 1)
                    return '大文件';
                return '超大文件';
            case 'photo':
                if (sizeBytes < 100 * 1024)
                    return '缩略图';
                if (sizeMB < 5)
                    return '普通照片';
                if (sizeMB < 50)
                    return '高清照片';
                return 'RAW文件';
            case 'video':
                if (sizeMB < 50)
                    return '短视频';
                if (sizeMB < 500)
                    return '标清视频';
                if (sizeGB < 2)
                    return '高清视频';
                return '4K视频';
            default:
                return '未分类';
        }
    }
    /**
     * 转换大小单位为字节
     */
    convertToBytes(size, unit) {
        switch (unit) {
            case 'KB': return size * 1024;
            case 'MB': return size * 1024 * 1024;
            case 'GB': return size * 1024 * 1024 * 1024;
            default: return size;
        }
    }
    /**
     * 计算相对日期
     */
    calculateRelativeDate(condition) {
        const now = new Date();
        const value = condition.relativeDateValue || 7;
        const unit = condition.relativeDateUnit || 'days';
        const direction = condition.relativeDateDirection || 'ago';
        let targetDate = new Date(now);
        // 根据单位和数值计算目标日期
        switch (unit) {
            case 'days':
                targetDate.setDate(now.getDate() + (direction === 'ago' ? -value : value));
                break;
            case 'weeks':
                targetDate.setDate(now.getDate() + (direction === 'ago' ? -value * 7 : value * 7));
                break;
            case 'months':
                targetDate.setMonth(now.getMonth() + (direction === 'ago' ? -value : value));
                break;
            case 'years':
                targetDate.setFullYear(now.getFullYear() + (direction === 'ago' ? -value : value));
                break;
        }
        // 返回 ISO 日期字符串格式
        return targetDate.toISOString().split('T')[0];
    }
    /**
     * 匹配相对日期条件
     */
    matchesRelativeDate(fileValue, condition, operator) {
        const now = new Date();
        const fileDate = new Date(fileValue);
        const value = condition.relativeDateValue || 7;
        const unit = condition.relativeDateUnit || 'days';
        const direction = condition.relativeDateDirection || 'ago';
        // 检查文件日期是否有效
        if (isNaN(fileDate.getTime())) {
            return false;
        }
        // 计算时间差（毫秒）
        const timeDiff = now.getTime() - fileDate.getTime();
        // 根据单位转换为对应的时间差
        let unitInMs;
        switch (unit) {
            case 'days':
                unitInMs = 24 * 60 * 60 * 1000;
                break;
            case 'weeks':
                unitInMs = 7 * 24 * 60 * 60 * 1000;
                break;
            case 'months':
                unitInMs = 30 * 24 * 60 * 60 * 1000; // 近似值
                break;
            case 'years':
                unitInMs = 365 * 24 * 60 * 60 * 1000; // 近似值
                break;
            default:
                unitInMs = 24 * 60 * 60 * 1000;
        }
        const targetTimeInMs = value * unitInMs;
        if (direction === 'ago') {
            // X时间前：文件日期应该早于(now - X时间)
            switch (operator) {
                case 'greaterThan':
                    return timeDiff > targetTimeInMs; // 文件比X时间前更早
                case 'lessThan':
                    return timeDiff < targetTimeInMs; // 文件比X时间前更晚
                case 'greaterThanOrEqual':
                    return timeDiff >= targetTimeInMs;
                case 'lessThanOrEqual':
                    return timeDiff <= targetTimeInMs;
                default:
                    return false;
            }
        }
        else {
            // X时间内：文件日期应该晚于(now - X时间)
            switch (operator) {
                case 'greaterThan':
                    return timeDiff < targetTimeInMs; // 文件在X时间内，且更接近现在
                case 'lessThan':
                    return timeDiff > targetTimeInMs; // 文件超出X时间范围
                case 'greaterThanOrEqual':
                    return timeDiff <= targetTimeInMs;
                case 'lessThanOrEqual':
                    return timeDiff >= targetTimeInMs;
                default:
                    return false;
            }
        }
    }
    /**
     * 获取文件或文件夹的指定字段值
     */
    getFileValue(file, field) {
        switch (field) {
            // 文件相关字段
            case 'fileName':
                return file.isDirectory ? '' : file.name;
            case 'fileExtension':
                return file.isDirectory ? '' : path_1.default.extname(file.name).slice(1).toLowerCase();
            case 'fileSize':
                return file.isDirectory ? 0 : file.size;
            case 'fileType':
                return file.isDirectory ? '' : this.getFileTypeCategory(path_1.default.extname(file.name).slice(1));
            case 'filePath':
                return file.isDirectory ? '' : file.path;
            // 文件夹相关字段
            case 'folderName':
                return file.isDirectory ? file.name : '';
            case 'folderSize':
                return file.isDirectory ? (file.totalSize || 0) : 0;
            case 'folderFileCount':
                return file.isDirectory ? (file.fileCount || 0) : 0;
            case 'folderSubfolderCount':
                return file.isDirectory ? (file.folderCount || 0) : 0;
            case 'folderIsEmpty':
                return file.isDirectory ? (file.isEmpty || false) : false;
            // 通用字段（文件和文件夹都适用）
            case 'createdDate':
                return file.createdDate || '';
            case 'modifiedDate':
                return file.modifiedDate || file.createdDate || '';
            case 'itemType':
                return file.isDirectory ? 'folder' : 'file';
            default:
                return '';
        }
    }
    /**
     * 计算新路径（执行用，会递增counter）
     */
    async calculateNewPathForExecution(currentPath, action) {
        if (action.type === 'delete') {
            return '将被删除';
        }
        if (action.type === 'rename') {
            const fileInfo = path_1.default.parse(currentPath);
            const newName = await this.generateFileName(fileInfo.base, action.config, action.id, currentPath);
            return path_1.default.join(fileInfo.dir, newName);
        }
        // 处理目标路径类型
        let targetPath;
        // 向后兼容：如果没有设置 targetPathType，根据 targetPath 是否存在来判断
        const pathType = action.config.targetPathType || (action.config.targetPath ? 'specific_path' : 'input_folder');
        if (pathType === 'input_folder') {
            // 使用输入文件夹作为目标路径
            targetPath = path_1.default.dirname(currentPath);
        }
        else {
            // 使用指定路径
            if (!action.config.targetPath) {
                return currentPath;
            }
            // 验证目标路径安全性
            if (!this.validatePath(action.config.targetPath)) {
                throw new Error(`Invalid or unsafe target path: ${action.config.targetPath}`);
            }
            targetPath = action.config.targetPath;
        }
        // 根据分类方式生成子文件夹路径
        const classificationPath = await this.generateClassificationPath(currentPath, action.config);
        if (classificationPath) {
            targetPath = path_1.default.join(targetPath, classificationPath);
        }
        // 对于preserveFolderStructure，moveWithStructure会自动处理文件名，需要返回完整的目标路径
        if (action.config.preserveFolderStructure) {
            const fileName = path_1.default.basename(currentPath);
            return path_1.default.join(targetPath, fileName);
        }
        // 生成新文件名
        const fileInfo = path_1.default.parse(currentPath);
        const newFileName = await this.generateFileName(fileInfo.base, action.config, action.id, currentPath);
        return path_1.default.join(targetPath, newFileName);
    }
    /**
     * 计算新路径（预览用）
     */
    async calculateNewPath(currentPath, action, fileIndex = 0, file) {
        if (action.type === 'delete') {
            return '将被删除';
        }
        if (action.type === 'rename') {
            const fileInfo = path_1.default.parse(currentPath);
            const newName = await this.generateFileNameForPreview(fileInfo.base, action.config, fileIndex, file);
            return path_1.default.join(fileInfo.dir, newName);
        }
        // 处理目标路径类型
        const fileInfo = path_1.default.parse(currentPath);
        let targetPath;
        // 向后兼容：如果没有设置 targetPathType，根据 targetPath 是否存在来判断
        const pathType = action.config.targetPathType || (action.config.targetPath ? 'specific_path' : 'input_folder');
        if (pathType === 'input_folder') {
            // 使用输入文件夹作为目标路径
            targetPath = path_1.default.dirname(currentPath);
        }
        else {
            // 使用指定路径
            if (!action.config.targetPath) {
                return currentPath;
            }
            // 验证目标路径安全性
            if (!this.validatePath(action.config.targetPath)) {
                throw new Error(`Invalid or unsafe target path: ${action.config.targetPath}`);
            }
            targetPath = action.config.targetPath;
        }
        // 根据分类方式生成子文件夹路径（预览用）
        const classificationPath = await this.generateClassificationPathForPreview(currentPath, action.config, file);
        if (classificationPath) {
            targetPath = path_1.default.join(targetPath, classificationPath);
        }
        // 对于preserveFolderStructure，moveWithStructure会自动处理文件名，这里只返回目标路径
        if (action.config.preserveFolderStructure) {
            return targetPath;
        }
        // 生成新文件名
        const newFileName = await this.generateFileNameForPreview(fileInfo.base, action.config, fileIndex, file);
        return path_1.default.join(targetPath, newFileName);
    }
    /**
     * 根据分类方式生成子文件夹路径（预览用）
     */
    async generateClassificationPathForPreview(filePath, config, file) {
        const classifyBy = config.classifyBy ||
            (config.createSubfolders ? 'fileType' : null) ||
            (config.preserveFolderStructure ? 'preserveStructure' : null);
        if (!classifyBy || classifyBy === 'none') {
            return '';
        }
        const fileInfo = path_1.default.parse(filePath);
        switch (classifyBy) {
            case 'fileType':
                return this.getFileTypeCategory(fileInfo.ext.slice(1));
            case 'createdDate':
                if (file?.createdDate) {
                    const date = new Date(file.createdDate);
                    return this.generateDateFolderPath(date, config.dateGrouping || 'yearMonth');
                }
                // 回退到文件系统日期
                try {
                    const stat = await fs_extra_1.default.stat(filePath);
                    return this.generateDateFolderPath(stat.birthtime, config.dateGrouping || 'yearMonth');
                }
                catch (error) {
                    return '未知日期';
                }
            case 'modifiedDate':
                if (file?.modifiedDate) {
                    const date = new Date(file.modifiedDate);
                    return this.generateDateFolderPath(date, config.dateGrouping || 'yearMonth');
                }
                // 回退到文件系统日期
                try {
                    const stat = await fs_extra_1.default.stat(filePath);
                    return this.generateDateFolderPath(stat.mtime, config.dateGrouping || 'yearMonth');
                }
                catch (error) {
                    return '未知日期';
                }
            case 'fileSize':
                const size = file?.size;
                if (size !== undefined) {
                    return this.generateSizeFolderPath(size, config);
                }
                // 回退到文件系统大小
                try {
                    const stat = await fs_extra_1.default.stat(filePath);
                    return this.generateSizeFolderPath(stat.size, config);
                }
                catch (error) {
                    return '未知大小';
                }
            case 'extension':
                const ext = fileInfo.ext.slice(1).toLowerCase();
                return ext || '无扩展名';
            case 'preserveStructure':
                // 保持文件夹结构：返回空字符串，由preserveFolderStructure逻辑处理
                return '';
            default:
                return '';
        }
    }
    /**
     * 为预览生成文件名（不会递增counter）
     */
    async generateFileNameForPreview(originalName, config, fileIndex, file) {
        const fileInfo = path_1.default.parse(originalName);
        const now = new Date();
        switch (config.namingPattern) {
            case 'timestamp':
                const timestamp = now.toISOString().replace(/[:.]/g, '-').slice(0, 19);
                const cleanOriginalForTimestampPreview = this.applyNameCleanup(path_1.default.parse(originalName).name, config);
                return `${timestamp}_${cleanOriginalForTimestampPreview}${path_1.default.parse(originalName).ext}`;
            case 'date':
                const dateFormat = config.dateFormat || 'YYYY-MM-DD';
                const dateStr = this.formatDate(now, dateFormat);
                const cleanOriginalForDatePreview = this.applyNameCleanup(path_1.default.parse(originalName).name, config);
                return `${dateStr}_${cleanOriginalForDatePreview}${path_1.default.parse(originalName).ext}`;
            case 'file-created':
                const cleanOriginalForCreatedPreview = this.applyNameCleanup(path_1.default.parse(originalName).name, config);
                const originalExtForCreatedPreview = path_1.default.parse(originalName).ext;
                if (file?.createdDate) {
                    try {
                        const createdDate = new Date(file.createdDate);
                        const createdDateFormat = config.dateFormat || 'YYYY-MM-DD';
                        const createdDateStr = this.formatDate(createdDate, createdDateFormat);
                        return `${createdDateStr}_${cleanOriginalForCreatedPreview}${originalExtForCreatedPreview}`;
                    }
                    catch (error) {
                        console.warn(`无法解析文件创建日期: ${file.createdDate}`, error);
                        // 回退到当前日期
                        const fallbackDateStr = this.formatDate(now, config.dateFormat || 'YYYY-MM-DD');
                        return `${fallbackDateStr}_${cleanOriginalForCreatedPreview}${originalExtForCreatedPreview}`;
                    }
                }
                else {
                    // 没有创建日期时回退到当前日期
                    const fallbackDateStr = this.formatDate(now, config.dateFormat || 'YYYY-MM-DD');
                    return `${fallbackDateStr}_${cleanOriginalForCreatedPreview}${originalExtForCreatedPreview}`;
                }
            case 'file-modified':
                const cleanOriginalForModifiedPreview = this.applyNameCleanup(path_1.default.parse(originalName).name, config);
                const originalExtForModifiedPreview = path_1.default.parse(originalName).ext;
                if (file?.modifiedDate) {
                    try {
                        const modifiedDate = new Date(file.modifiedDate);
                        const modifiedDateFormat = config.dateFormat || 'YYYY-MM-DD';
                        const modifiedDateStr = this.formatDate(modifiedDate, modifiedDateFormat);
                        return `${modifiedDateStr}_${cleanOriginalForModifiedPreview}${originalExtForModifiedPreview}`;
                    }
                    catch (error) {
                        console.warn(`无法解析文件修改日期: ${file.modifiedDate}`, error);
                        // 回退到当前日期
                        const fallbackDateStr = this.formatDate(now, config.dateFormat || 'YYYY-MM-DD');
                        return `${fallbackDateStr}_${cleanOriginalForModifiedPreview}${originalExtForModifiedPreview}`;
                    }
                }
                else {
                    // 没有修改日期时，尝试从文件路径获取
                    if (file?.path) {
                        try {
                            const stat = await fs_extra_1.default.stat(file.path);
                            const modifiedDate = stat.mtime;
                            const modifiedDateFormat = config.dateFormat || 'YYYY-MM-DD';
                            const modifiedDateStr = this.formatDate(modifiedDate, modifiedDateFormat);
                            return `${modifiedDateStr}_${cleanOriginalForModifiedPreview}${originalExtForModifiedPreview}`;
                        }
                        catch (error) {
                            console.warn(`无法获取文件修改日期: ${file.path}`, error);
                        }
                    }
                    // 回退到当前日期
                    const fallbackDateStr = this.formatDate(now, config.dateFormat || 'YYYY-MM-DD');
                    return `${fallbackDateStr}_${cleanOriginalForModifiedPreview}${originalExtForModifiedPreview}`;
                }
            case 'counter':
                const counterStart = config.counterStart || 1;
                const counterPadding = config.counterPadding || 3;
                const counter = (counterStart + fileIndex).toString().padStart(counterPadding, '0');
                const cleanOriginalForCounterPreview = this.applyNameCleanup(path_1.default.parse(originalName).name, config);
                const originalExtForCounterPreview = path_1.default.parse(originalName).ext;
                return `${counter}_${cleanOriginalForCounterPreview}${originalExtForCounterPreview}`;
            case 'prefix':
                return this.applyPrefix(originalName, config.prefix || '', config);
            case 'suffix':
                return this.applySuffix(originalName, config.suffix || '', config);
            case 'replace':
                return this.applyReplace(originalName, config.replaceFrom || '', config.replaceTo || '', config);
            case 'case':
                return this.applyCase(originalName, config);
            case 'custom':
                if (config.customPattern) {
                    return this.applyCustomPatternForPreview(config.customPattern, originalName, now, config, fileIndex);
                }
                return originalName;
            case 'advanced':
                return this.applyAdvancedRules(originalName, config.advancedRules || [], now);
            case 'original':
            default:
                return originalName;
        }
    }
    /**
     * 为预览应用自定义命名模式（不会递增counter）
     */
    applyCustomPatternForPreview(pattern, originalName, date, config, fileIndex = 0) {
        const fileInfo = path_1.default.parse(originalName);
        // 处理counter配置
        const counterStart = config?.counterStart || 1;
        const counterPadding = config?.counterPadding || 3;
        const counterValue = (counterStart + fileIndex).toString().padStart(counterPadding, '0');
        let fileName = fileInfo.name;
        // 应用removeSpaces和removeSpecialChars到原始文件名
        if (config?.removeSpaces) {
            fileName = fileName.replace(/\s+/g, '');
        }
        if (config?.removeSpecialChars) {
            fileName = fileName.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '');
        }
        let result = pattern
            .replace('{name}', fileName)
            .replace('{ext}', fileInfo.ext)
            .replace('{date}', this.formatDate(date, 'YYYY-MM-DD'))
            .replace('{time}', date.toTimeString().slice(0, 8).replace(/:/g, '-'))
            .replace('{counter}', counterValue)
            .replace('{type}', this.getFileTypeCategory(fileInfo.ext.slice(1)))
            .replace('{year}', date.getFullYear().toString())
            .replace('{month}', (date.getMonth() + 1).toString().padStart(2, '0'))
            .replace('{day}', date.getDate().toString().padStart(2, '0'))
            .replace('{hour}', date.getHours().toString().padStart(2, '0'))
            .replace('{minute}', date.getMinutes().toString().padStart(2, '0'))
            .replace('{second}', date.getSeconds().toString().padStart(2, '0'));
        // 如果用户没有在模式中包含{ext}，自动添加原文件扩展名
        if (!pattern.includes('{ext}') && fileInfo.ext) {
            result += fileInfo.ext;
        }
        return result;
    }
    /**
     * 应用removeSpaces和removeSpecialChars到文件名
     */
    applyNameCleanup(fileName, config) {
        let cleanName = fileName;
        if (config?.removeSpaces) {
            cleanName = cleanName.replace(/\s+/g, '');
        }
        if (config?.removeSpecialChars) {
            cleanName = cleanName.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '');
        }
        return cleanName;
    }
    /**
     * 验证文件名安全性
     */
    validateFileName(fileName) {
        // Windows和Unix系统的非法字符
        const invalidChars = /[<>:"/\\|?*\x00-\x1f]/;
        if (invalidChars.test(fileName)) {
            return { isValid: false, error: `文件名包含非法字符: ${fileName}` };
        }
        // Windows保留名称
        const reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'];
        const nameWithoutExt = path_1.default.parse(fileName).name.toUpperCase();
        if (reservedNames.includes(nameWithoutExt)) {
            return { isValid: false, error: `文件名使用了系统保留名称: ${fileName}` };
        }
        // 文件名长度检查（Windows限制为255字符）
        if (fileName.length > 255) {
            return { isValid: false, error: `文件名过长（超过255字符）: ${fileName}` };
        }
        // 不能以点或空格结尾
        if (fileName.endsWith('.') || fileName.endsWith(' ')) {
            return { isValid: false, error: `文件名不能以点或空格结尾: ${fileName}` };
        }
        return { isValid: true };
    }
    /**
     * 生成新文件名
     */
    async generateFileName(originalName, config, actionId, filePath) {
        const fileInfo = path_1.default.parse(originalName);
        const now = new Date();
        let newFileName;
        switch (config.namingPattern) {
            case 'timestamp':
                const timestamp = now.toISOString().replace(/[:.]/g, '-').slice(0, 19);
                const cleanOriginalForTimestamp = this.applyNameCleanup(path_1.default.parse(originalName).name, config);
                newFileName = `${timestamp}_${cleanOriginalForTimestamp}${path_1.default.parse(originalName).ext}`;
                break;
            case 'date':
                const dateFormat = config.dateFormat || 'YYYY-MM-DD';
                const dateStr = this.formatDate(now, dateFormat);
                const cleanOriginalForDate = this.applyNameCleanup(path_1.default.parse(originalName).name, config);
                newFileName = `${dateStr}_${cleanOriginalForDate}${path_1.default.parse(originalName).ext}`;
                break;
            case 'file-created':
                const cleanOriginalForCreated = this.applyNameCleanup(path_1.default.parse(originalName).name, config);
                const originalExt = path_1.default.parse(originalName).ext;
                if (filePath) {
                    try {
                        const stat = await fs_extra_1.default.stat(filePath);
                        const createdDate = stat.birthtime;
                        const createdDateFormat = config.dateFormat || 'YYYY-MM-DD';
                        const createdDateStr = this.formatDate(createdDate, createdDateFormat);
                        newFileName = `${createdDateStr}_${cleanOriginalForCreated}${originalExt}`;
                    }
                    catch (error) {
                        console.warn(`无法获取文件创建日期: ${filePath}`, error);
                        // 回退到当前日期
                        const fallbackDateStr = this.formatDate(now, config.dateFormat || 'YYYY-MM-DD');
                        newFileName = `${fallbackDateStr}_${cleanOriginalForCreated}${originalExt}`;
                    }
                }
                else {
                    // 没有文件路径时回退到当前日期
                    const fallbackDateStr = this.formatDate(now, config.dateFormat || 'YYYY-MM-DD');
                    newFileName = `${fallbackDateStr}_${cleanOriginalForCreated}${originalExt}`;
                }
                break;
            case 'file-modified':
                const cleanOriginalForModified = this.applyNameCleanup(path_1.default.parse(originalName).name, config);
                const originalExtForModified = path_1.default.parse(originalName).ext;
                if (filePath) {
                    try {
                        const stat = await fs_extra_1.default.stat(filePath);
                        const modifiedDate = stat.mtime;
                        const modifiedDateFormat = config.dateFormat || 'YYYY-MM-DD';
                        const modifiedDateStr = this.formatDate(modifiedDate, modifiedDateFormat);
                        newFileName = `${modifiedDateStr}_${cleanOriginalForModified}${originalExtForModified}`;
                    }
                    catch (error) {
                        console.warn(`无法获取文件修改日期: ${filePath}`, error);
                        // 回退到当前日期
                        const fallbackDateStr = this.formatDate(now, config.dateFormat || 'YYYY-MM-DD');
                        newFileName = `${fallbackDateStr}_${cleanOriginalForModified}${originalExtForModified}`;
                    }
                }
                else {
                    // 没有文件路径时回退到当前日期
                    const fallbackDateStr = this.formatDate(now, config.dateFormat || 'YYYY-MM-DD');
                    newFileName = `${fallbackDateStr}_${cleanOriginalForModified}${originalExtForModified}`;
                }
                break;
            case 'counter':
                // 获取当前counter值并递增
                let counter;
                if (actionId) {
                    const counterStart = config.counterStart || 1;
                    const counterPadding = config.counterPadding || 3;
                    const currentCounter = this.counterMap.get(actionId) || counterStart;
                    counter = currentCounter.toString().padStart(counterPadding, '0');
                    this.counterMap.set(actionId, currentCounter + 1);
                }
                else {
                    counter = (config.counterStart || 1).toString().padStart(config.counterPadding || 3, '0');
                }
                const cleanOriginalForCounter = this.applyNameCleanup(path_1.default.parse(originalName).name, config);
                const originalExtForCounter = path_1.default.parse(originalName).ext;
                newFileName = `${counter}_${cleanOriginalForCounter}${originalExtForCounter}`;
                break;
            case 'prefix':
                newFileName = this.applyPrefix(originalName, config.prefix || '', config);
                break;
            case 'suffix':
                newFileName = this.applySuffix(originalName, config.suffix || '', config);
                break;
            case 'replace':
                newFileName = this.applyReplace(originalName, config.replaceFrom || '', config.replaceTo || '', config);
                break;
            case 'case':
                newFileName = this.applyCase(originalName, config);
                break;
            case 'custom':
                if (config.customPattern) {
                    newFileName = this.applyCustomPattern(config.customPattern, originalName, now, config, actionId);
                }
                else {
                    newFileName = originalName;
                }
                break;
            case 'advanced':
                newFileName = this.applyAdvancedRules(originalName, config.advancedRules || [], now);
                break;
            case 'original':
            default:
                newFileName = originalName;
                break;
        }
        // 验证生成的文件名
        const validation = this.validateFileName(newFileName);
        if (!validation.isValid) {
            throw new Error(validation.error || '生成的文件名无效');
        }
        return newFileName;
    }
    /**
     * 格式化日期
     */
    formatDate(date, format) {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return format
            .replace('YYYY', year.toString())
            .replace('MM', month)
            .replace('DD', day);
    }
    /**
     * 应用自定义命名模式
     */
    applyCustomPattern(pattern, originalName, date, config, actionId) {
        const fileInfo = path_1.default.parse(originalName);
        // 处理counter配置
        const counterStart = config?.counterStart || 1;
        const counterPadding = config?.counterPadding || 3;
        // 获取当前counter值并递增
        let counterValue;
        if (actionId && pattern.includes('{counter}')) {
            const currentCounter = this.counterMap.get(actionId) || counterStart;
            counterValue = currentCounter.toString().padStart(counterPadding, '0');
            this.counterMap.set(actionId, currentCounter + 1);
        }
        else {
            counterValue = counterStart.toString().padStart(counterPadding, '0');
        }
        let fileName = fileInfo.name;
        // 应用removeSpaces和removeSpecialChars到原始文件名
        if (config?.removeSpaces) {
            fileName = fileName.replace(/\s+/g, '');
        }
        if (config?.removeSpecialChars) {
            fileName = fileName.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '');
        }
        let result = pattern
            .replace('{name}', fileName)
            .replace('{ext}', fileInfo.ext)
            .replace('{date}', this.formatDate(date, 'YYYY-MM-DD'))
            .replace('{time}', date.toTimeString().slice(0, 8).replace(/:/g, '-'))
            .replace('{counter}', counterValue)
            .replace('{type}', this.getFileTypeCategory(fileInfo.ext.slice(1)))
            .replace('{year}', date.getFullYear().toString())
            .replace('{month}', (date.getMonth() + 1).toString().padStart(2, '0'))
            .replace('{day}', date.getDate().toString().padStart(2, '0'))
            .replace('{hour}', date.getHours().toString().padStart(2, '0'))
            .replace('{minute}', date.getMinutes().toString().padStart(2, '0'))
            .replace('{second}', date.getSeconds().toString().padStart(2, '0'));
        // 如果用户没有在模式中包含{ext}，自动添加原文件扩展名
        if (!pattern.includes('{ext}') && fileInfo.ext) {
            result += fileInfo.ext;
        }
        return result;
    }
    /**
     * 添加前缀
     */
    applyPrefix(originalName, prefix, config) {
        const fileInfo = path_1.default.parse(originalName);
        let newName = fileInfo.name;
        // 应用removeSpaces和removeSpecialChars
        if (config?.removeSpaces) {
            newName = newName.replace(/\s+/g, '');
        }
        if (config?.removeSpecialChars) {
            newName = newName.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '');
        }
        return `${prefix}${newName}${fileInfo.ext}`;
    }
    /**
     * 添加后缀
     */
    applySuffix(originalName, suffix, config) {
        const fileInfo = path_1.default.parse(originalName);
        let newName = fileInfo.name;
        // 应用removeSpaces和removeSpecialChars
        if (config?.removeSpaces) {
            newName = newName.replace(/\s+/g, '');
        }
        if (config?.removeSpecialChars) {
            newName = newName.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '');
        }
        return `${newName}${suffix}${fileInfo.ext}`;
    }
    /**
     * 查找替换
     */
    applyReplace(originalName, replaceFrom, replaceTo, config) {
        const fileInfo = path_1.default.parse(originalName);
        let newName = fileInfo.name.replace(new RegExp(replaceFrom, 'g'), replaceTo);
        // 应用removeSpaces和removeSpecialChars
        if (config?.removeSpaces) {
            newName = newName.replace(/\s+/g, '');
        }
        if (config?.removeSpecialChars) {
            newName = newName.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '');
        }
        return `${newName}${fileInfo.ext}`;
    }
    /**
     * 大小写转换
     */
    applyCase(originalName, config) {
        const fileInfo = path_1.default.parse(originalName);
        let newName = fileInfo.name;
        // 移除空格和特殊字符
        if (config.removeSpaces) {
            newName = newName.replace(/\s+/g, '');
        }
        if (config.removeSpecialChars) {
            newName = newName.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '');
        }
        // 应用大小写转换
        switch (config.caseType) {
            case 'lower':
                newName = newName.toLowerCase();
                break;
            case 'upper':
                newName = newName.toUpperCase();
                break;
            case 'title':
                newName = newName.replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());
                break;
            case 'camel':
                newName = newName.replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => index === 0 ? word.toLowerCase() : word.toUpperCase()).replace(/\s+/g, '');
                break;
            case 'pascal':
                newName = newName.replace(/(?:^\w|[A-Z]|\b\w)/g, (word) => word.toUpperCase()).replace(/\s+/g, '');
                break;
            case 'snake':
                newName = newName.toLowerCase().replace(/\s+/g, '_');
                break;
            case 'kebab':
                newName = newName.toLowerCase().replace(/\s+/g, '-');
                break;
        }
        return `${newName}${fileInfo.ext}`;
    }
    /**
     * 应用高级组合规则
     */
    applyAdvancedRules(originalName, rules, date) {
        const fileInfo = path_1.default.parse(originalName);
        let result = fileInfo.name;
        // 按顺序应用规则
        const sortedRules = rules
            .filter(rule => rule.enabled)
            .sort((a, b) => a.order - b.order);
        for (const rule of sortedRules) {
            switch (rule.type) {
                case 'prefix':
                    result = `${rule.value}${result}`;
                    break;
                case 'suffix':
                    result = `${result}${rule.value}`;
                    break;
                case 'replace':
                    if (rule.config?.replaceFrom) {
                        result = result.replace(new RegExp(rule.config.replaceFrom, 'g'), rule.config.replaceTo || '');
                    }
                    break;
                case 'case':
                    result = this.applyCaseTransform(result, rule.config?.caseType || 'lower');
                    break;
                case 'counter':
                    const counter = (rule.config?.counterStart || 1)
                        .toString()
                        .padStart(rule.config?.counterPadding || 3, '0');
                    result = `${result}${rule.value.replace('{counter}', counter)}`;
                    break;
                case 'date':
                    const dateStr = this.formatDate(date, rule.config?.dateFormat || 'YYYY-MM-DD');
                    result = `${result}${rule.value.replace('{date}', dateStr)}`;
                    break;
                case 'custom':
                    result = this.applyCustomPattern(rule.value, `${result}${fileInfo.ext}`, date, rule.config);
                    result = path_1.default.parse(result).name; // 移除扩展名，因为会在最后添加
                    break;
            }
        }
        return `${result}${fileInfo.ext}`;
    }
    /**
     * 应用大小写转换（辅助方法）
     */
    applyCaseTransform(text, caseType) {
        switch (caseType) {
            case 'lower':
                return text.toLowerCase();
            case 'upper':
                return text.toUpperCase();
            case 'title':
                return text.replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());
            case 'camel':
                return text.replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => index === 0 ? word.toLowerCase() : word.toUpperCase()).replace(/\s+/g, '');
            case 'pascal':
                return text.replace(/(?:^\w|[A-Z]|\b\w)/g, (word) => word.toUpperCase()).replace(/\s+/g, '');
            case 'snake':
                return text.toLowerCase().replace(/\s+/g, '_');
            case 'kebab':
                return text.toLowerCase().replace(/\s+/g, '-');
            default:
                return text;
        }
    }
    /**
     * 执行动作
     */
    async executeActions(currentPath, actions) {
        let resultPath = currentPath;
        let fileExists = true;
        for (const action of actions) {
            if (!fileExists && action.type !== 'delete') {
                // 如果文件已被删除，跳过非删除操作
                console.warn(`跳过动作 ${action.type}，因为文件已被删除: ${resultPath}`);
                continue;
            }
            const newPath = await this.executeAction(resultPath, action);
            if (action.type === 'delete') {
                fileExists = false;
                // 删除操作后，保持原路径用于记录，但标记文件不存在
                resultPath = currentPath;
            }
            else {
                resultPath = newPath;
            }
        }
        return resultPath;
    }
    /**
     * 执行单个动作
     */
    async executeAction(currentPath, action) {
        if (!action.enabled) {
            return currentPath;
        }
        try {
            const newPath = await this.calculateNewPathForExecution(currentPath, action);
            // 验证操作的安全性和可行性
            await this.validateOperation(currentPath, newPath, action.type);
            if (action.type === 'delete') {
                await this.executeDelete(currentPath, action.config);
                return currentPath;
            }
            // 确保目标目录存在
            if (action.config.createSubfolders !== false) {
                const targetDir = path_1.default.dirname(newPath);
                await this.ensureDirWithTracking(targetDir);
            }
            switch (action.type) {
                case 'move':
                    await this.executeMove(currentPath, newPath, action.config);
                    return newPath;
                case 'copy':
                    await this.executeCopy(currentPath, newPath, action.config);
                    return newPath;
                case 'rename':
                    await fs_extra_1.default.move(currentPath, newPath, { overwrite: true });
                    return newPath;
                case 'createFolder':
                    // 创建文件夹功能
                    await this.ensureDirWithTracking(newPath);
                    console.log(`创建文件夹: ${newPath}`);
                    return newPath;
                default:
                    return currentPath;
            }
        }
        catch (error) {
            console.error(`执行动作失败: ${action.type}`, error);
            const categorizedError = error instanceof Error
                ? this.categorizeError(error, action.type, currentPath)
                : `${action.type}失败: ${currentPath} - ${String(error)}`;
            throw new Error(categorizedError);
        }
    }
    /**
     * 执行删除操作
     */
    async executeDelete(targetPath, config) {
        try {
            // 检查是否为文件夹
            const stat = await fs_extra_1.default.stat(targetPath);
            if (stat.isDirectory()) {
                // 检查文件夹是否为空
                const items = await fs_extra_1.default.readdir(targetPath);
                if (items.length > 0 && !config.deleteNonEmptyFolders) {
                    throw new Error(`文件夹不为空，无法删除: ${targetPath}`);
                }
                // 使用 trash 库安全删除文件夹（无论是否为空）
                const { default: trash } = await Promise.resolve().then(() => __importStar(require('trash')));
                await trash([targetPath]);
            }
            else {
                // 删除文件
                const { default: trash } = await Promise.resolve().then(() => __importStar(require('trash')));
                await trash([targetPath]);
            }
            console.log(`删除成功: ${targetPath}`);
        }
        catch (error) {
            console.error(`删除操作失败: ${targetPath}`, error);
            throw error;
        }
    }
    /**
     * 执行移动操作
     */
    async executeMove(sourcePath, targetPath, config) {
        try {
            // 跟踪源文件的父目录（移动后可能变空）
            const sourceDir = path_1.default.dirname(sourcePath);
            this.processedDirectories.add(sourceDir);
            if (config.preserveFolderStructure) {
                // 对于preserveFolderStructure，targetPath已经是完整的目标路径，直接移动
                // 确保目标目录存在
                const targetDir = path_1.default.dirname(targetPath);
                await this.ensureDirWithTracking(targetDir);
                await fs_extra_1.default.move(sourcePath, targetPath, { overwrite: true });
            }
            else {
                // 直接使用 fs.move 的原生行为，完整移动文件夹及其所有内容
                await fs_extra_1.default.move(sourcePath, targetPath, { overwrite: true });
            }
        }
        catch (error) {
            console.error(`移动操作失败: ${sourcePath} -> ${targetPath}`, error);
            throw error;
        }
    }
    /**
     * 执行复制操作
     */
    async executeCopy(sourcePath, targetPath, config) {
        try {
            if (config.preserveFolderStructure) {
                // 对于preserveFolderStructure，targetPath已经是完整的目标路径，直接复制
                // 确保目标目录存在
                const targetDir = path_1.default.dirname(targetPath);
                await this.ensureDirWithTracking(targetDir);
                await fs_extra_1.default.copy(sourcePath, targetPath, { overwrite: true });
            }
            else {
                // 直接使用 fs.copy 的原生行为，完整复制文件夹及其所有内容
                await fs_extra_1.default.copy(sourcePath, targetPath, { overwrite: true });
            }
        }
        catch (error) {
            console.error(`复制操作失败: ${sourcePath} -> ${targetPath}`, error);
            throw error;
        }
    }
    /**
     * 清理空文件夹
     */
    async cleanupEmptyFolders(dirPath) {
        try {
            // 检查目录是否存在
            if (!await fs_extra_1.default.pathExists(dirPath)) {
                return;
            }
            // 获取目录内容
            const items = await fs_extra_1.default.readdir(dirPath);
            // 如果目录为空，删除它
            if (items.length === 0) {
                console.log(`删除空文件夹: ${dirPath}`);
                await fs_extra_1.default.rmdir(dirPath);
                // 递归检查父目录
                const parentDir = path_1.default.dirname(dirPath);
                if (parentDir !== dirPath) { // 避免无限递归
                    await this.cleanupEmptyFolders(parentDir);
                }
            }
        }
        catch (error) {
            console.error(`清理空文件夹失败: ${dirPath}`, error);
            // 不抛出错误，避免影响主要操作
        }
    }
    /**
     * 根据文件扩展名智能分类文件类型
     */
    getFileTypeCategory(extension) {
        const ext = extension.toLowerCase();
        // 图片文件 - 扩展支持更多格式
        const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico', 'tiff', 'tif', 'psd', 'ai', 'eps', 'raw', 'cr2', 'nef', 'arw', 'dng', 'heic', 'heif', 'avif'];
        if (imageExts.includes(ext))
            return '图片';
        // 文档文件 - 扩展支持更多格式
        const documentExts = ['pdf', 'doc', 'docx', 'txt', 'rtf', 'xls', 'xlsx', 'ppt', 'pptx', 'odt', 'ods', 'odp', 'pages', 'numbers', 'key', 'epub', 'mobi', 'azw', 'azw3'];
        if (documentExts.includes(ext))
            return '文档';
        // 视频文件 - 扩展支持更多格式
        const videoExts = ['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm', 'm4v', '3gp', 'mpg', 'mpeg', 'mts', 'mxf', 'vob', 'ts', 'f4v', 'rm', 'rmvb', 'asf'];
        if (videoExts.includes(ext))
            return '视频';
        // 音频文件 - 扩展支持更多格式
        const audioExts = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a', 'opus', 'ape', 'alac', 'aiff', 'au', 'ra', 'amr', 'ac3', 'dts'];
        if (audioExts.includes(ext))
            return '音频';
        // 压缩文件 - 扩展支持更多格式
        const archiveExts = ['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz', 'cab', 'iso', 'dmg', 'img', 'bin', 'cue', 'mdf', 'nrg', 'udf', 'lzh', 'ace', 'arj'];
        if (archiveExts.includes(ext))
            return '压缩包';
        // 代码文件 - 大幅扩展支持
        const codeExts = [
            // Web前端
            'html', 'htm', 'css', 'scss', 'sass', 'less', 'js', 'jsx', 'ts', 'tsx', 'vue', 'svelte',
            // 后端语言
            'py', 'java', 'cpp', 'c', 'h', 'hpp', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt', 'scala', 'clj', 'hs', 'ml', 'fs', 'vb', 'pas', 'pl', 'r', 'lua', 'dart', 'elm',
            // 脚本语言
            'sh', 'bash', 'zsh', 'fish', 'ps1', 'bat', 'cmd', 'vbs',
            // 配置文件
            'json', 'xml', 'yaml', 'yml', 'toml', 'ini', 'cfg', 'conf', 'properties', 'env',
            // 数据库
            'sql', 'mysql', 'pgsql', 'sqlite',
            // 标记语言
            'md', 'markdown', 'rst', 'tex', 'latex',
            // 其他
            'dockerfile', 'makefile', 'cmake', 'gradle', 'maven', 'sbt'
        ];
        if (codeExts.includes(ext))
            return '代码';
        // 数据文件 - 新增分类
        const dataExts = ['csv', 'tsv', 'json', 'xml', 'yaml', 'yml', 'sql', 'db', 'sqlite', 'sqlite3', 'mdb', 'accdb', 'dbf', 'parquet', 'avro', 'orc'];
        if (dataExts.includes(ext))
            return '数据';
        // 3D模型文件 - 新增分类
        const modelExts = ['obj', 'fbx', 'dae', 'blend', 'max', '3ds', 'stl', 'ply', 'x3d', 'gltf', 'glb', 'usd', 'usda', 'usdc', 'abc'];
        if (modelExts.includes(ext))
            return '3D模型';
        // 字体文件 - 新增分类
        const fontExts = ['ttf', 'otf', 'woff', 'woff2', 'eot', 'pfb', 'pfm', 'afm', 'bdf', 'pcf'];
        if (fontExts.includes(ext))
            return '字体';
        // 快捷方式文件 - 新增分类
        const shortcutExts = ['lnk', 'url'];
        if (shortcutExts.includes(ext))
            return '快捷方式';
        // 可执行文件 - 扩展支持
        const executableExts = ['exe', 'msi', 'dmg', 'pkg', 'deb', 'rpm', 'appimage', 'snap', 'flatpak', 'app', 'ipa', 'apk', 'xap'];
        if (executableExts.includes(ext))
            return '程序';
        // CAD文件 - 新增分类
        const cadExts = ['dwg', 'dxf', 'step', 'stp', 'iges', 'igs', 'sat', 'brep', 'x_t', 'x_b'];
        if (cadExts.includes(ext))
            return 'CAD';
        // 电子书 - 从文档中分离出来
        const ebookExts = ['epub', 'mobi', 'azw', 'azw3', 'fb2', 'lit', 'pdb', 'prc', 'lrf', 'tcr'];
        if (ebookExts.includes(ext))
            return '电子书';
        return '其他';
    }
    /**
     * 确保目录存在并跟踪创建的目录
     */
    async ensureDirWithTracking(dirPath) {
        // 检查目录是否已存在
        const exists = await fs_extra_1.default.pathExists(dirPath);
        if (!exists) {
            // 在创建目录之前，先记录哪些父目录不存在
            const dirsToCreate = [];
            let currentPath = dirPath;
            // 从目标路径向上遍历，找出所有不存在的目录
            while (currentPath && currentPath !== path_1.default.dirname(currentPath)) {
                if (!await fs_extra_1.default.pathExists(currentPath)) {
                    dirsToCreate.unshift(currentPath); // 添加到数组开头，保持从父到子的顺序
                }
                else {
                    break; // 找到存在的父目录，停止遍历
                }
                currentPath = path_1.default.dirname(currentPath);
            }
            // 创建目录
            await fs_extra_1.default.ensureDir(dirPath);
            // 将所有新创建的目录添加到跟踪集合中
            for (const dir of dirsToCreate) {
                this.createdDirectories.add(dir);
                console.log(`📁 跟踪创建的目录: ${dir}`);
            }
        }
    }
    /**
     * 清理工作流创建的空文件夹（软件内部清理机制）
     */
    async cleanupCreatedEmptyDirectories() {
        if (this.createdDirectories.size === 0) {
            return;
        }
        console.log(`开始清理 ${this.createdDirectories.size} 个可能的空文件夹...`);
        // 按路径深度排序，从最深的开始清理
        const sortedDirs = Array.from(this.createdDirectories).sort((a, b) => {
            const depthA = a.split(path_1.default.sep).length;
            const depthB = b.split(path_1.default.sep).length;
            return depthB - depthA; // 深度大的在前
        });
        let cleanedCount = 0;
        for (const dirPath of sortedDirs) {
            try {
                // 检查目录是否存在
                if (!await fs_extra_1.default.pathExists(dirPath)) {
                    continue;
                }
                // 检查目录是否为空
                const items = await fs_extra_1.default.readdir(dirPath);
                if (items.length === 0) {
                    await fs_extra_1.default.rmdir(dirPath);
                    cleanedCount++;
                    console.log(`已清理空文件夹: ${dirPath}`);
                }
            }
            catch (error) {
                // 忽略清理错误，不影响主要功能
                console.warn(`清理文件夹失败 ${dirPath}:`, error);
            }
        }
        if (cleanedCount > 0) {
            console.log(`✅ 成功清理了 ${cleanedCount} 个空文件夹`);
        }
        else {
            console.log(`ℹ️ 没有发现需要清理的空文件夹`);
        }
        // 注意：不在这里清空 createdDirectories，因为历史记录还需要使用这些信息
        // 清空操作将在历史记录创建后由 clearCreatedDirectories() 方法执行
    }
    /**
     * 清理处理过程中遇到的所有空文件夹（用户功能）
     */
    async cleanupAllProcessedEmptyDirectories() {
        if (this.processedDirectories.size === 0) {
            console.log('ℹ️ 没有处理过程中的目录需要检查');
            return;
        }
        console.log(`开始检查 ${this.processedDirectories.size} 个处理过程中的目录...`);
        // 按路径深度排序，从最深的开始清理
        const sortedDirs = Array.from(this.processedDirectories).sort((a, b) => {
            const depthA = a.split(path_1.default.sep).length;
            const depthB = b.split(path_1.default.sep).length;
            return depthB - depthA; // 深度大的在前
        });
        let cleanedCount = 0;
        for (const dirPath of sortedDirs) {
            try {
                // 检查目录是否存在
                if (!await fs_extra_1.default.pathExists(dirPath)) {
                    continue;
                }
                // 检查目录是否为空
                const items = await fs_extra_1.default.readdir(dirPath);
                if (items.length === 0) {
                    await fs_extra_1.default.rmdir(dirPath);
                    cleanedCount++;
                    // 记录被清理的空文件夹（用于撤销时恢复）
                    this.cleanedEmptyDirectories.add(dirPath);
                    console.log(`已清理空文件夹: ${dirPath}`);
                    // 递归检查父目录是否也变空了
                    await this.checkAndCleanupParentDirectory(dirPath, true);
                }
            }
            catch (error) {
                // 忽略清理错误，不影响主要功能
                console.warn(`清理文件夹失败 ${dirPath}:`, error);
            }
        }
        if (cleanedCount > 0) {
            console.log(`✅ 成功清理了 ${cleanedCount} 个处理过程中的空文件夹`);
        }
        else {
            console.log(`ℹ️ 处理过程中的目录都不为空，无需清理`);
        }
    }
    /**
     * 递归检查并清理父目录（如果为空）
     */
    async checkAndCleanupParentDirectory(childPath, trackCleaned = false) {
        const parentPath = path_1.default.dirname(childPath);
        // 避免清理根目录或系统重要目录
        if (parentPath === childPath || parentPath === '/' || parentPath.match(/^[A-Z]:\\?$/)) {
            return;
        }
        try {
            // 检查父目录是否存在且为空
            if (await fs_extra_1.default.pathExists(parentPath)) {
                const items = await fs_extra_1.default.readdir(parentPath);
                if (items.length === 0) {
                    await fs_extra_1.default.rmdir(parentPath);
                    console.log(`已清理空的父文件夹: ${parentPath}`);
                    // 如果需要跟踪，记录被清理的父文件夹
                    if (trackCleaned) {
                        this.cleanedEmptyDirectories.add(parentPath);
                    }
                    // 继续检查上级父目录
                    await this.checkAndCleanupParentDirectory(parentPath, trackCleaned);
                }
            }
        }
        catch (error) {
            // 忽略父目录清理错误
            console.warn(`清理父文件夹失败 ${parentPath}:`, error);
        }
    }
    /**
     * 检查目录是否为空
     */
    async isEmptyDirectory(dirPath) {
        try {
            // 检查路径是否存在
            if (!await fs_extra_1.default.pathExists(dirPath)) {
                return false;
            }
            // 检查是否为目录
            const stat = await fs_extra_1.default.stat(dirPath);
            if (!stat.isDirectory()) {
                return false;
            }
            // 检查目录是否为空
            const items = await fs_extra_1.default.readdir(dirPath);
            return items.length === 0;
        }
        catch (error) {
            console.warn(`检查目录是否为空时出错 ${dirPath}:`, error);
            return false;
        }
    }
    /**
     * 重新扫描原始目录
     */
    async rescanOriginalDirectories(currentFiles, processTarget = 'files', processSubfolders = true, maxDepth = -1) {
        // 按原始目录分组
        const originalDirGroups = new Map();
        for (const file of currentFiles) {
            const originalDir = file.originalDir || (file.isDirectory ? file.path : path_1.default.dirname(file.path));
            if (!originalDirGroups.has(originalDir)) {
                originalDirGroups.set(originalDir, []);
            }
            originalDirGroups.get(originalDir).push(file);
        }
        console.log(`重新扫描 ${originalDirGroups.size} 个原始目录`);
        // 并行扫描所有原始目录
        const scanPromises = Array.from(originalDirGroups.keys()).map(async (originalDir) => {
            try {
                return await this.loadItemsFromPathWithCache(originalDir, processTarget, processSubfolders, maxDepth);
            }
            catch (error) {
                console.warn(`重新扫描目录失败: ${originalDir}`, error);
                return [];
            }
        });
        const results = await Promise.all(scanPromises);
        const allFiles = results.flat();
        console.log(`重新扫描完成，找到 ${allFiles.length} 个项目`);
        return allFiles;
    }
    /**
     * 带缓存的目录扫描
     */
    async loadItemsFromPathWithCache(targetPath, processTarget = 'files', processSubfolders = true, maxDepth = -1) {
        const cacheKey = `${targetPath}:${processTarget}:${processSubfolders}:${maxDepth}`;
        const cached = this.dirScanCache.get(cacheKey);
        const now = Date.now();
        // 缓存5秒内有效
        if (cached && (now - cached.timestamp) < 5000) {
            console.log(`使用缓存扫描结果: ${targetPath}`);
            return cached.files;
        }
        const files = await this.loadItemsFromPath(targetPath, processTarget, processSubfolders, maxDepth);
        // 在添加新缓存前清理过期缓存
        this.cleanupCache();
        this.dirScanCache.set(cacheKey, { files, timestamp: now });
        return files;
    }
    /**
     * 获取工作流执行过程中创建的文件夹列表
     */
    getCreatedDirectories() {
        return Array.from(this.createdDirectories);
    }
    /**
     * 获取并保存创建的文件夹列表（用于历史记录）
     * 这个方法会在清理之前调用，确保历史记录能获取到完整的文件夹列表
     */
    getAndPreserveCreatedDirectories() {
        const directories = Array.from(this.createdDirectories);
        console.log('🔒 保存创建的文件夹列表用于历史记录:', directories);
        console.log('🔒 这些文件夹将在撤销时被清理（如果为空）');
        return directories;
    }
    /**
     * 清空创建的文件夹列表（在历史记录创建后调用）
     */
    clearCreatedDirectories() {
        console.log('🧹 清空创建文件夹的跟踪列表');
        this.createdDirectories.clear();
    }
    /**
     * 获取被清理的空文件夹列表
     */
    getCleanedEmptyDirectories() {
        return Array.from(this.cleanedEmptyDirectories);
    }
    /**
     * 获取并保存被清理的空文件夹列表（用于历史记录）
     */
    getAndPreserveCleanedEmptyDirectories() {
        const directories = Array.from(this.cleanedEmptyDirectories);
        console.log('🔒 保存被清理的空文件夹列表用于历史记录:', directories);
        console.log('🔒 这些文件夹将在撤销时被恢复');
        return directories;
    }
    /**
     * 清空被清理空文件夹的跟踪列表（在历史记录创建后调用）
     */
    clearCleanedEmptyDirectories() {
        console.log('🧹 清空被清理空文件夹的跟踪列表');
        this.cleanedEmptyDirectories.clear();
    }
}
exports.WorkflowEngine = WorkflowEngine;
